# Windows 部署故障排除指南

## 🚨 常见问题及解决方案

### 1. 模块路径错误 (Module not found)

**问题描述**: 出现类似 `Module "xxx" does not exist in container` 的错误

**解决方案**:
```bash
# 1. 清理缓存和重新安装
npm run clean
npm install

# 2. 使用Windows专用启动命令
npm run dev:windows

# 3. 或者使用批处理文件
start-windows.bat
```

### 2. 端口占用问题

**问题描述**: `EADDRINUSE: address already in use :::8000`

**解决方案**:
```bash
# 查找占用端口的进程
netstat -ano | findstr :8000

# 结束进程 (替换PID为实际进程ID)
taskkill /PID <PID> /F

# 或者使用不同端口启动
set PORT=3000 && npm run dev:windows
```

### 3. 内存不足错误

**问题描述**: `JavaScript heap out of memory`

**解决方案**:
```bash
# 使用Windows专用脚本 (已设置内存限制)
npm run dev:windows

# 或手动设置
set NODE_OPTIONS=--max-old-space-size=4096 && npm start
```

### 4. 路径分隔符问题

**问题描述**: 路径使用了错误的分隔符

**解决方案**:
- 已在 `.umirc.ts` 中配置了跨平台兼容性
- 确保使用正斜杠 `/` 而不是反斜杠 `\`

### 5. 权限问题

**问题描述**: `EPERM: operation not permitted`

**解决方案**:
```bash
# 以管理员身份运行命令提示符
# 或者清理npm缓存
npm cache clean --force
```

## 🔧 推荐的启动流程

### 方法1: 使用批处理文件 (推荐)
```bash
# 双击运行或在命令行执行
start-windows.bat
```

### 方法2: 手动命令
```bash
# 1. 环境检查
node check-env.js

# 2. 安装依赖 (如果需要)
npm install

# 3. 启动开发服务器
npm run dev:windows
```

### 方法3: 完全重置
```bash
# 如果遇到严重问题，完全重置
npm run reinstall
npm run dev:windows
```

## 📋 环境要求

- **Node.js**: 16.x 或 18.x (推荐 18.x)
- **npm**: 8.x 或更高版本
- **操作系统**: Windows 10/11
- **内存**: 至少 4GB 可用内存

## 🔍 调试技巧

### 查看详细错误信息
```bash
# 启用详细日志
set DEBUG=* && npm run dev:windows

# 或者查看UMI调试信息
set UMI_DEBUG=1 && npm run dev:windows
```

### 检查网络连接
```bash
# 测试本地服务器
curl http://localhost:8000

# 或在浏览器中访问
http://localhost:8000
```

## 📞 获取帮助

如果以上方案都无法解决问题，请提供以下信息：

1. **系统信息**: 运行 `node check-env.js` 的输出
2. **错误日志**: 完整的错误信息截图
3. **Node.js版本**: `node --version` 和 `npm --version`
4. **操作步骤**: 详细描述出现问题前的操作

## 🎯 成功启动的标志

当看到以下信息时，表示启动成功：
```
✔ Webpack
  Compiled successfully

 DONE  Compiled successfully
```

然后可以在浏览器中访问: http://localhost:8000
