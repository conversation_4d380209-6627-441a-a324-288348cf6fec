@echo off
echo ========================================
echo   Test Paper Generation System
echo   Windows Startup Script
echo ========================================
echo.

REM 检查Node.js是否安装
echo [1/6] Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js found:
node --version

REM 检查npm是否可用
echo [2/6] Checking npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: npm is not available
    pause
    exit /b 1
)
echo ✅ npm found:
npm --version

REM 清理可能的缓存问题
echo [3/6] Cleaning cache and temporary files...
if exist ".umi" (
    echo Removing .umi directory...
    rmdir /s /q ".umi"
)
if exist "node_modules\.cache" (
    echo Removing node_modules cache...
    rmdir /s /q "node_modules\.cache"
)

REM 检查并安装依赖
echo [4/6] Checking dependencies...
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Error: Failed to install dependencies
        echo Try running: npm cache clean --force
        pause
        exit /b 1
    )
) else (
    echo ✅ Dependencies found
)

REM 设置环境变量
echo [5/6] Setting environment variables...
set NODE_OPTIONS=--max-old-space-size=4096
set FORCE_COLOR=1
set UMI_ENV=development

echo [6/6] Starting development server...
echo.
echo 🚀 Server will start at: http://localhost:8000
echo 📝 Press Ctrl+C to stop the server
echo.

REM 启动开发服务器
echo Starting server with Windows optimizations...
npm run dev:windows

if %errorlevel% neq 0 (
    echo.
    echo ❌ Server failed to start. Common solutions:
    echo    1. Run: fix-module-error.bat
    echo    2. Check if port 8000 is occupied: netstat -ano ^| findstr :8000
    echo    3. Try running as Administrator
    echo    4. Check Node.js version: node --version
    echo.
    echo Press any key to run diagnosis tool...
    pause >nul
    node windows-diagnosis.js
) else (
    echo.
    echo ✅ Server started successfully!
    echo    Access: http://localhost:8000
)

echo.
echo Server stopped.
pause
