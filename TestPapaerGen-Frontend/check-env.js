const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🔍 环境检查开始...\n');

// 检查操作系统
console.log('📋 系统信息:');
console.log(`   操作系统: ${os.type()} ${os.release()}`);
console.log(`   架构: ${os.arch()}`);
console.log(`   Node.js版本: ${process.version}`);
console.log(`   npm版本: ${process.env.npm_version || '未知'}`);
console.log('');

// 检查必要文件
console.log('📁 文件检查:');
const requiredFiles = [
  'package.json',
  '.umirc.ts',
  'src',
  'src/pages'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  console.log(`   ${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

// 检查node_modules
const nodeModulesExists = fs.existsSync(path.join(__dirname, 'node_modules'));
console.log(`   ${nodeModulesExists ? '✅' : '❌'} node_modules`);

// 检查关键依赖
if (nodeModulesExists) {
  console.log('\n📦 关键依赖检查:');
  const keyDeps = ['umi', 'react', 'antd'];
  keyDeps.forEach(dep => {
    const depPath = path.join(__dirname, 'node_modules', dep);
    const exists = fs.existsSync(depPath);
    console.log(`   ${exists ? '✅' : '❌'} ${dep}`);
  });
}

// 检查端口
console.log('\n🌐 网络检查:');
const net = require('net');
const checkPort = (port) => {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.listen(port, () => {
      server.once('close', () => {
        resolve(true);
      });
      server.close();
    });
    server.on('error', () => {
      resolve(false);
    });
  });
};

checkPort(8000).then(available => {
  console.log(`   端口8000: ${available ? '✅ 可用' : '❌ 被占用'}`);
  
  console.log('\n📝 建议操作:');
  if (!nodeModulesExists) {
    console.log('   1. 运行: npm install');
  }
  if (!available) {
    console.log('   2. 端口8000被占用，请关闭占用进程或使用其他端口');
  }
  if (allFilesExist && nodeModulesExists && available) {
    console.log('   ✅ 环境检查通过，可以启动开发服务器');
    console.log('   运行: npm run dev:windows');
  }
  console.log('\n🔍 环境检查完成');
});
