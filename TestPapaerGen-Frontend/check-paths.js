const fs = require('fs');
const path = require('path');

console.log('🔍 路径兼容性检查工具\n');

// 检查所有JS文件中的导入路径
function checkImportPaths(dir, results = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      checkImportPaths(fullPath, results);
    } else if (file.endsWith('.js') || file.endsWith('.jsx') || file.endsWith('.ts') || file.endsWith('.tsx')) {
      try {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // 检查相对路径导入
        const relativeImports = content.match(/import.*from\s+['"]\.\.?\/[^'"]*['"]/g) || [];
        const requireImports = content.match(/require\(['"]\.\.?\/[^'"]*['"]\)/g) || [];
        
        // 检查绝对路径导入
        const absoluteImports = content.match(/import.*from\s+['"]@\/[^'"]*['"]/g) || [];
        
        // 检查可能的路径问题
        const backslashPaths = content.match(/['"].*\\.*['"]/g) || [];
        
        if (relativeImports.length > 0 || requireImports.length > 0 || absoluteImports.length > 0 || backslashPaths.length > 0) {
          results.push({
            file: fullPath.replace(__dirname, '').replace(/\\/g, '/'),
            relativeImports,
            requireImports, 
            absoluteImports,
            backslashPaths
          });
        }
      } catch (e) {
        // 忽略读取错误
      }
    }
  });
  
  return results;
}

console.log('📁 扫描项目文件...');
const pathIssues = checkImportPaths('./src');

console.log('\n📊 路径使用统计:');
let totalRelative = 0;
let totalAbsolute = 0;
let totalBackslash = 0;

pathIssues.forEach(issue => {
  totalRelative += issue.relativeImports.length + issue.requireImports.length;
  totalAbsolute += issue.absoluteImports.length;
  totalBackslash += issue.backslashPaths.length;
});

console.log(`   相对路径导入: ${totalRelative} 个`);
console.log(`   绝对路径导入: ${totalAbsolute} 个`);
console.log(`   反斜杠路径: ${totalBackslash} 个 ${totalBackslash > 0 ? '⚠️' : '✅'}`);

if (totalBackslash > 0) {
  console.log('\n⚠️  发现反斜杠路径问题:');
  pathIssues.forEach(issue => {
    if (issue.backslashPaths.length > 0) {
      console.log(`   文件: ${issue.file}`);
      issue.backslashPaths.forEach(path => {
        console.log(`     ${path}`);
      });
    }
  });
  console.log('\n💡 建议: 将所有反斜杠 \\ 替换为正斜杠 /');
}

console.log('\n🔧 路径规范建议:');
console.log('   ✅ 使用正斜杠: import xxx from "./path/to/file"');
console.log('   ✅ 使用@别名: import xxx from "@/pages/home"');
console.log('   ❌ 避免反斜杠: import xxx from ".\\path\\to\\file"');
console.log('   ❌ 避免绝对路径: import xxx from "C:/project/src/..."');

// 检查UMI路由配置
console.log('\n🛣️  路由配置检查:');
try {
  const umiConfig = fs.readFileSync('.umirc.ts', 'utf8');
  const routeMatches = umiConfig.match(/component:\s*['"]@\/[^'"]*['"]/g) || [];
  
  console.log(`   找到 ${routeMatches.length} 个路由组件配置`);
  
  routeMatches.forEach(route => {
    const componentPath = route.match(/@\/([^'"]*)/)[1];
    const actualPath = path.join('./src', componentPath + '.js');
    const exists = fs.existsSync(actualPath);
    console.log(`   ${exists ? '✅' : '❌'} ${route}`);
    
    if (!exists) {
      // 尝试其他扩展名
      const alternatives = ['.jsx', '.ts', '.tsx'];
      let found = false;
      for (let ext of alternatives) {
        if (fs.existsSync(path.join('./src', componentPath + ext))) {
          console.log(`     💡 找到: ${componentPath}${ext}`);
          found = true;
          break;
        }
      }
      if (!found) {
        console.log(`     ❌ 文件不存在: ${componentPath}`);
      }
    }
  });
} catch (e) {
  console.log('   ❌ 无法读取路由配置');
}

console.log('\n✅ 路径检查完成');

// Windows特定建议
if (process.platform === 'win32') {
  console.log('\n🪟 Windows特定建议:');
  console.log('   1. 确保所有路径使用正斜杠 /');
  console.log('   2. 避免路径中包含空格和特殊字符');
  console.log('   3. 使用相对路径或@别名，避免绝对路径');
  console.log('   4. 文件名保持一致的大小写');
}
