/**
 * ECharts 离线配置工具
 * 解决国内网络访问外部API的问题
 */

import * as echarts from 'echarts/core';
import { PieChart, LineChart, BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册所有必要的组件
echarts.use([
  PieChart,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  CanvasRenderer
]);

/**
 * 配置ECharts为离线模式
 * 禁用所有外部网络请求
 */
export const configureEChartsOffline = () => {
  // 禁用地图数据的自动加载
  if (echarts.registerMap) {
    echarts.registerMap = () => {
      console.warn('ECharts地图功能已禁用以避免网络请求');
    };
  }

  // 设置全局配置
  echarts.init = (function(originalInit) {
    return function(dom, theme, opts) {
      const defaultOpts = {
        renderer: 'canvas',
        useDirtyRect: false,
        useCoarsePointer: false,
        pointerSize: 180,
        ssr: false,
        width: null,
        height: null,
        locale: 'ZH'
      };
      
      const mergedOpts = Object.assign({}, defaultOpts, opts);
      return originalInit.call(this, dom, theme, mergedOpts);
    };
  })(echarts.init);

  console.log('✅ ECharts已配置为离线模式');
};

/**
 * 通用的图表主题配置
 */
export const goldTheme = {
  color: [
    'rgb(245, 210, 128)',  // 主金黄色
    'rgb(255, 235, 180)',  // 浅金色
    'rgb(139, 117, 61)',   // 深金色
    'rgb(200, 170, 100)',  // 中金色
    'rgb(255, 220, 150)',  // 亮金色
    'rgb(180, 150, 80)',   // 暗金色
    'rgb(255, 240, 200)',  // 极浅金色
    'rgb(160, 135, 70)'    // 更深金色
  ],
  backgroundColor: 'transparent',
  textStyle: {
    color: 'rgb(139, 117, 61)',
    fontFamily: 'Arial, sans-serif'
  },
  tooltip: {
    backgroundColor: 'rgba(255, 248, 220, 0.9)',
    borderColor: 'rgb(245, 210, 128)',
    textStyle: {
      color: 'rgb(139, 117, 61)'
    }
  }
};

/**
 * 创建安全的图表配置
 * 确保不会触发外部网络请求
 */
export const createSafeChartOption = (baseOption) => {
  const safeOption = JSON.parse(JSON.stringify(baseOption));
  
  // 移除可能导致网络请求的配置
  if (safeOption.geo) {
    delete safeOption.geo;
    console.warn('已移除geo配置以避免网络请求');
  }
  
  if (safeOption.bmap) {
    delete safeOption.bmap;
    console.warn('已移除bmap配置以避免网络请求');
  }
  
  if (safeOption.amap) {
    delete safeOption.amap;
    console.warn('已移除amap配置以避免网络请求');
  }

  // 确保使用本地渲染
  if (safeOption.animation === undefined) {
    safeOption.animation = true;
  }
  
  return safeOption;
};

/**
 * 检查网络连接状态
 */
export const checkNetworkStatus = () => {
  return new Promise((resolve) => {
    if (!navigator.onLine) {
      resolve(false);
      return;
    }
    
    // 尝试访问一个快速的本地资源
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = '/favicon.png?' + Date.now();
    
    // 超时处理
    setTimeout(() => resolve(false), 3000);
  });
};

/**
 * 初始化ECharts环境
 */
export const initEChartsEnvironment = async () => {
  try {
    // 配置离线模式
    configureEChartsOffline();
    
    // 检查网络状态
    const isOnline = await checkNetworkStatus();
    
    if (!isOnline) {
      console.warn('⚠️ 网络连接不稳定，ECharts将使用离线模式');
    } else {
      console.log('✅ 网络连接正常，ECharts离线模式已启用');
    }
    
    return {
      success: true,
      isOnline,
      message: 'ECharts环境初始化成功'
    };
  } catch (error) {
    console.error('❌ ECharts环境初始化失败:', error);
    return {
      success: false,
      isOnline: false,
      message: error.message
    };
  }
};

// 导出echarts实例
export { echarts };

// 自动初始化
if (typeof window !== 'undefined') {
  initEChartsEnvironment();
}
