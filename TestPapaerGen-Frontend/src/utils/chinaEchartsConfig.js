/**
 * ECharts 国内镜像配置
 * 使用国内CDN和镜像源解决网络访问问题
 */

// 国内CDN镜像列表
const CHINA_CDN_MIRRORS = {
  // 字节跳动CDN
  bytedance: 'https://lf3-cdn-tos.bytescm.com/obj/static/',
  // 七牛云CDN  
  qiniu: 'https://cdn.staticfile.org/',
  // 又拍云CDN
  upyun: 'https://unpkg.zhimg.com/',
  // 饿了么CDN
  eleme: 'https://npm.elemecdn.com/',
  // 腾讯云CDN
  tencent: 'https://cdn.bootcdn.net/ajax/libs/',
  // 百度CDN
  baidu: 'https://code.bdstatic.com/npm/'
};

// 国内地图数据源
const CHINA_MAP_DATA_SOURCES = {
  // 高德地图API
  amap: 'https://webapi.amap.com/maps',
  // 百度地图API  
  bmap: 'https://api.map.baidu.com',
  // 腾讯地图API
  tmap: 'https://apis.map.qq.com'
};

/**
 * 配置ECharts使用国内镜像
 */
export const configureEChartsForChina = () => {
  // 设置默认的CDN源为国内镜像
  if (typeof window !== 'undefined') {
    window.__ECHARTS_CDN_BASE__ = CHINA_CDN_MIRRORS.eleme;
    
    // 禁用可能导致网络问题的外部请求
    window.__DISABLE_EXTERNAL_MAP_REQUESTS__ = true;
    
    console.log('✅ ECharts已配置为使用国内CDN镜像');
  }
};

/**
 * 创建适合国内网络的ECharts配置
 */
export const createChinaFriendlyOption = (baseOption) => {
  const option = JSON.parse(JSON.stringify(baseOption));
  
  // 移除可能导致外网访问的配置
  if (option.geo) {
    // 如果需要地图，使用本地数据
    option.geo.map = 'china'; // 使用内置的中国地图
    delete option.geo.roam; // 禁用漫游以避免额外请求
  }
  
  // 优化动画性能
  if (!option.animation) {
    option.animation = {
      duration: 1000,
      easing: 'cubicOut'
    };
  }
  
  // 设置离线友好的工具提示
  if (option.tooltip) {
    option.tooltip.confine = true; // 限制在图表区域内
    option.tooltip.appendToBody = false; // 不添加到body
  }
  
  return option;
};

/**
 * 国内网络环境检测
 */
export const detectChinaNetwork = async () => {
  const testUrls = [
    'https://www.baidu.com/favicon.ico',
    'https://cdn.staticfile.org/echarts/5.3.2/echarts.min.js',
    'https://npm.elemecdn.com/echarts@5.3.2/dist/echarts.min.js'
  ];
  
  const results = await Promise.allSettled(
    testUrls.map(url => 
      fetch(url, { method: 'HEAD', mode: 'no-cors' })
        .then(() => ({ url, accessible: true }))
        .catch(() => ({ url, accessible: false }))
    )
  );
  
  const accessibleUrls = results
    .filter(result => result.status === 'fulfilled')
    .map(result => result.value)
    .filter(result => result.accessible);
    
  return {
    isInChina: accessibleUrls.length > 0,
    accessibleCDNs: accessibleUrls,
    recommendedCDN: accessibleUrls.length > 0 ? 
      CHINA_CDN_MIRRORS.eleme : CHINA_CDN_MIRRORS.qiniu
  };
};

/**
 * 金黄色主题 - 适配国内用户习惯
 */
export const chinaGoldTheme = {
  color: [
    '#F5D280', // 主金黄色
    '#FFEB80', // 浅金色  
    '#D4AF37', // 金色
    '#FFD700', // 黄金色
    '#FFA500', // 橙金色
    '#DAA520', // 深金色
    '#B8860B', // 暗金色
    '#CD853F'  // 沙棕色
  ],
  backgroundColor: 'transparent',
  textStyle: {
    fontFamily: '"Microsoft YaHei", "微软雅黑", Arial, sans-serif',
    color: '#8B753D'
  },
  title: {
    textStyle: {
      fontFamily: '"Microsoft YaHei", "微软雅黑", Arial, sans-serif',
      color: '#8B753D',
      fontWeight: 'bold'
    }
  },
  tooltip: {
    backgroundColor: 'rgba(255, 248, 220, 0.95)',
    borderColor: '#F5D280',
    borderWidth: 1,
    textStyle: {
      color: '#8B753D',
      fontFamily: '"Microsoft YaHei", "微软雅黑", Arial, sans-serif'
    }
  },
  legend: {
    textStyle: {
      color: '#8B753D',
      fontFamily: '"Microsoft YaHei", "微软雅黑", Arial, sans-serif'
    }
  }
};

/**
 * 替代图表库推荐
 */
export const alternativeChartLibraries = {
  // 阿里巴巴的G2图表库
  g2: {
    name: 'G2',
    description: '阿里巴巴开源的可视化图形语法库',
    url: 'https://g2.antv.vision/',
    pros: ['国内开发', '文档中文', '性能优秀', '社区活跃'],
    suitable: ['数据可视化', '商业图表', '移动端']
  },
  
  // 百度的Chart.js
  chartjs: {
    name: 'Chart.js',
    description: '轻量级图表库，国内CDN支持好',
    url: 'https://www.chartjs.org/',
    pros: ['轻量级', '易上手', 'CDN支持好', '兼容性强'],
    suitable: ['简单图表', '快速开发', '移动端']
  },
  
  // 腾讯的TCharts
  tcharts: {
    name: 'TCharts',
    description: '腾讯开源的图表库',
    url: 'https://github.com/Tencent/TCharts',
    pros: ['腾讯出品', '性能优化', '移动端友好'],
    suitable: ['移动应用', '小程序', 'H5页面']
  }
};

/**
 * 初始化国内网络环境
 */
export const initChinaEnvironment = async () => {
  try {
    // 检测网络环境
    const networkInfo = await detectChinaNetwork();
    
    // 配置ECharts
    configureEChartsForChina();
    
    // 设置字体
    if (typeof document !== 'undefined') {
      const style = document.createElement('style');
      style.textContent = `
        .echarts-container {
          font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
        }
      `;
      document.head.appendChild(style);
    }
    
    console.log('🇨🇳 国内网络环境配置完成');
    console.log('📊 推荐CDN:', networkInfo.recommendedCDN);
    
    return {
      success: true,
      networkInfo,
      message: '国内环境配置成功'
    };
  } catch (error) {
    console.error('❌ 国内环境配置失败:', error);
    return {
      success: false,
      error: error.message,
      message: '建议使用离线模式'
    };
  }
};

// 自动检测并配置
if (typeof window !== 'undefined') {
  // 检测是否在国内网络环境
  const isLikelyChina = 
    navigator.language.includes('zh') || 
    Intl.DateTimeFormat().resolvedOptions().timeZone.includes('Asia/Shanghai') ||
    new Date().getTimezoneOffset() === -480; // UTC+8
    
  if (isLikelyChina) {
    initChinaEnvironment();
  }
}

export default {
  configureEChartsForChina,
  createChinaFriendlyOption,
  detectChinaNetwork,
  chinaGoldTheme,
  alternativeChartLibraries,
  initChinaEnvironment
};
