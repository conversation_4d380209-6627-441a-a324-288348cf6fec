// 全局样式文件
@import './styles/theme.less';

// 主题颜色定义
@primary-color: rgb(245, 210, 128);
@primary-light: rgb(255, 248, 220);
@primary-dark: rgb(139, 117, 61);
@text-color: rgb(51, 51, 51);

// 全局重置样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, @primary-light 0%, rgba(255, 255, 255, 0.95) 100%);
  min-height: 100vh;

  // 全局字体渲染优化
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern" 1;
  font-kerning: normal;
}

// 优化页面顶部区域背景
body {
  background-attachment: fixed;
  background-repeat: no-repeat;

  // 添加微妙的纹理
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(245, 210, 128, 0.03) 1px, transparent 1px),
      radial-gradient(circle at 80% 80%, rgba(245, 210, 128, 0.02) 1px, transparent 1px);
    background-size: 60px 60px, 40px 40px;
    pointer-events: none;
    z-index: -1;
  }
}

#root {
  min-height: 100vh;
}

// 全局滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: @primary-light;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: @primary-color;
  border-radius: 4px;
  
  &:hover {
    background: @primary-dark;
  }
}

// 全局动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

// 卡片样式
.modern-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(245, 210, 128, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

// 全局Ant Design组件样式优化
.ant-card {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(245, 210, 128, 0.3) !important;
  box-shadow: 0 4px 12px rgba(245, 210, 128, 0.1) !important;

  &:hover {
    box-shadow: 0 6px 16px rgba(245, 210, 128, 0.2) !important;
  }

  .ant-card-head {
    background: linear-gradient(135deg, rgba(245, 210, 128, 0.1) 0%, rgba(255, 248, 220, 0.5) 100%) !important;
    border-bottom: 1px solid rgba(245, 210, 128, 0.3) !important;
  }
}

// 按钮样式优化
.ant-btn-primary {
  background: linear-gradient(135deg, @primary-color 0%, @primary-dark 100%) !important;
  border-color: @primary-color !important;
  box-shadow: 0 2px 8px rgba(245, 210, 128, 0.3) !important;

  &:hover, &:focus {
    background: linear-gradient(135deg, @primary-dark 0%, @primary-color 100%) !important;
    border-color: @primary-dark !important;
    box-shadow: 0 4px 12px rgba(245, 210, 128, 0.4) !important;
  }
}

// 输入框样式优化
.ant-input, .ant-input-number, .ant-select-selector {
  border-color: rgba(245, 210, 128, 0.4) !important;

  &:hover {
    border-color: @primary-color !important;
  }

  &:focus, &.ant-input-focused, &.ant-select-focused .ant-select-selector {
    border-color: @primary-color !important;
    box-shadow: 0 0 0 2px rgba(245, 210, 128, 0.2) !important;
  }
}

// 表格样式优化
.ant-table {
  background: rgba(255, 255, 255, 0.95) !important;

  .ant-table-thead > tr > th {
    background: linear-gradient(135deg, rgba(245, 210, 128, 0.1) 0%, rgba(255, 248, 220, 0.5) 100%) !important;
    border-bottom: 1px solid rgba(245, 210, 128, 0.3) !important;
  }

  .ant-table-tbody > tr:hover > td {
    background: rgba(245, 210, 128, 0.05) !important;
  }
}

// 全局字体优化
* {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 标题字体优化
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  letter-spacing: -0.02em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 正文字体优化
p, span, div {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Ant Design 组件字体优化
.ant-typography {
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

.ant-btn {
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-weight: 500 !important;
}

.ant-menu-item {
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}
