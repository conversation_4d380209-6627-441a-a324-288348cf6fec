(self["webpackChunk"] = self["webpackChunk"] || []).push([["mf-dep_vendors-node_modules_echarts_lib_component_marker_installMarkPoint_js"],{

/***/ "./node_modules/echarts/lib/component/marker/MarkPointModel.js":
/*!*********************************************************************!*\
  !*** ./node_modules/echarts/lib/component/marker/MarkPointModel.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ "./node_modules/tslib/tslib.es6.js");
/* harmony import */ var _MarkerModel_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MarkerModel.js */ "./node_modules/echarts/lib/component/marker/MarkerModel.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


var MarkPointModel = /** @class */function (_super) {
  (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(MarkPointModel, _super);
  function MarkPointModel() {
    var _this = _super !== null && _super.apply(this, arguments) || this;
    _this.type = MarkPointModel.type;
    return _this;
  }
  MarkPointModel.prototype.createMarkerModelFromSeries = function (markerOpt, masterMarkerModel, ecModel) {
    return new MarkPointModel(markerOpt, masterMarkerModel, ecModel);
  };
  MarkPointModel.type = 'markPoint';
  MarkPointModel.defaultOption = {
    // zlevel: 0,
    z: 5,
    symbol: 'pin',
    symbolSize: 50,
    // symbolRotate: 0,
    // symbolOffset: [0, 0]
    tooltip: {
      trigger: 'item'
    },
    label: {
      show: true,
      position: 'inside'
    },
    itemStyle: {
      borderWidth: 2
    },
    emphasis: {
      label: {
        show: true
      }
    }
  };
  return MarkPointModel;
}(_MarkerModel_js__WEBPACK_IMPORTED_MODULE_1__.default);
/* harmony default export */ __webpack_exports__["default"] = (MarkPointModel);

/***/ }),

/***/ "./node_modules/echarts/lib/component/marker/MarkPointView.js":
/*!********************************************************************!*\
  !*** ./node_modules/echarts/lib/component/marker/MarkPointView.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ "./node_modules/tslib/tslib.es6.js");
/* harmony import */ var _chart_helper_SymbolDraw_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../chart/helper/SymbolDraw.js */ "./node_modules/echarts/lib/chart/helper/SymbolDraw.js");
/* harmony import */ var _util_number_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/number.js */ "./node_modules/echarts/lib/util/number.js");
/* harmony import */ var _data_SeriesData_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../data/SeriesData.js */ "./node_modules/echarts/lib/data/SeriesData.js");
/* harmony import */ var _markerHelper_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./markerHelper.js */ "./node_modules/echarts/lib/component/marker/markerHelper.js");
/* harmony import */ var _MarkerView_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MarkerView.js */ "./node_modules/echarts/lib/component/marker/MarkerView.js");
/* harmony import */ var _MarkerModel_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarkerModel.js */ "./node_modules/echarts/lib/component/marker/MarkerModel.js");
/* harmony import */ var zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zrender/lib/core/util.js */ "./node_modules/zrender/lib/core/util.js");
/* harmony import */ var _util_innerStore_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../util/innerStore.js */ "./node_modules/echarts/lib/util/innerStore.js");
/* harmony import */ var _visual_helper_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../visual/helper.js */ "./node_modules/echarts/lib/visual/helper.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/










function updateMarkerLayout(mpData, seriesModel, api) {
  var coordSys = seriesModel.coordinateSystem;
  mpData.each(function (idx) {
    var itemModel = mpData.getItemModel(idx);
    var point;
    var xPx = _util_number_js__WEBPACK_IMPORTED_MODULE_0__.parsePercent(itemModel.get('x'), api.getWidth());
    var yPx = _util_number_js__WEBPACK_IMPORTED_MODULE_0__.parsePercent(itemModel.get('y'), api.getHeight());
    if (!isNaN(xPx) && !isNaN(yPx)) {
      point = [xPx, yPx];
    }
    // Chart like bar may have there own marker positioning logic
    else if (seriesModel.getMarkerPosition) {
      // Use the getMarkerPosition
      point = seriesModel.getMarkerPosition(mpData.getValues(mpData.dimensions, idx));
    } else if (coordSys) {
      var x = mpData.get(coordSys.dimensions[0], idx);
      var y = mpData.get(coordSys.dimensions[1], idx);
      point = coordSys.dataToPoint([x, y]);
    }
    // Use x, y if has any
    if (!isNaN(xPx)) {
      point[0] = xPx;
    }
    if (!isNaN(yPx)) {
      point[1] = yPx;
    }
    mpData.setItemLayout(idx, point);
  });
}
var MarkPointView = /** @class */function (_super) {
  (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__extends)(MarkPointView, _super);
  function MarkPointView() {
    var _this = _super !== null && _super.apply(this, arguments) || this;
    _this.type = MarkPointView.type;
    return _this;
  }
  MarkPointView.prototype.updateTransform = function (markPointModel, ecModel, api) {
    ecModel.eachSeries(function (seriesModel) {
      var mpModel = _MarkerModel_js__WEBPACK_IMPORTED_MODULE_2__.default.getMarkerModelFromSeries(seriesModel, 'markPoint');
      if (mpModel) {
        updateMarkerLayout(mpModel.getData(), seriesModel, api);
        this.markerGroupMap.get(seriesModel.id).updateLayout();
      }
    }, this);
  };
  MarkPointView.prototype.renderSeries = function (seriesModel, mpModel, ecModel, api) {
    var coordSys = seriesModel.coordinateSystem;
    var seriesId = seriesModel.id;
    var seriesData = seriesModel.getData();
    var symbolDrawMap = this.markerGroupMap;
    var symbolDraw = symbolDrawMap.get(seriesId) || symbolDrawMap.set(seriesId, new _chart_helper_SymbolDraw_js__WEBPACK_IMPORTED_MODULE_3__.default());
    var mpData = createData(coordSys, seriesModel, mpModel);
    // FIXME
    mpModel.setData(mpData);
    updateMarkerLayout(mpModel.getData(), seriesModel, api);
    mpData.each(function (idx) {
      var itemModel = mpData.getItemModel(idx);
      var symbol = itemModel.getShallow('symbol');
      var symbolSize = itemModel.getShallow('symbolSize');
      var symbolRotate = itemModel.getShallow('symbolRotate');
      var symbolOffset = itemModel.getShallow('symbolOffset');
      var symbolKeepAspect = itemModel.getShallow('symbolKeepAspect');
      // TODO: refactor needed: single data item should not support callback function
      if ((0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.isFunction)(symbol) || (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.isFunction)(symbolSize) || (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.isFunction)(symbolRotate) || (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.isFunction)(symbolOffset)) {
        var rawIdx = mpModel.getRawValue(idx);
        var dataParams = mpModel.getDataParams(idx);
        if ((0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.isFunction)(symbol)) {
          symbol = symbol(rawIdx, dataParams);
        }
        if ((0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.isFunction)(symbolSize)) {
          // FIXME 这里不兼容 ECharts 2.x，2.x 貌似参数是整个数据？
          symbolSize = symbolSize(rawIdx, dataParams);
        }
        if ((0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.isFunction)(symbolRotate)) {
          symbolRotate = symbolRotate(rawIdx, dataParams);
        }
        if ((0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.isFunction)(symbolOffset)) {
          symbolOffset = symbolOffset(rawIdx, dataParams);
        }
      }
      var style = itemModel.getModel('itemStyle').getItemStyle();
      var color = (0,_visual_helper_js__WEBPACK_IMPORTED_MODULE_5__.getVisualFromData)(seriesData, 'color');
      if (!style.fill) {
        style.fill = color;
      }
      mpData.setItemVisual(idx, {
        symbol: symbol,
        symbolSize: symbolSize,
        symbolRotate: symbolRotate,
        symbolOffset: symbolOffset,
        symbolKeepAspect: symbolKeepAspect,
        style: style
      });
    });
    // TODO Text are wrong
    symbolDraw.updateData(mpData);
    this.group.add(symbolDraw.group);
    // Set host model for tooltip
    // FIXME
    mpData.eachItemGraphicEl(function (el) {
      el.traverse(function (child) {
        (0,_util_innerStore_js__WEBPACK_IMPORTED_MODULE_6__.getECData)(child).dataModel = mpModel;
      });
    });
    this.markKeep(symbolDraw);
    symbolDraw.group.silent = mpModel.get('silent') || seriesModel.get('silent');
  };
  MarkPointView.type = 'markPoint';
  return MarkPointView;
}(_MarkerView_js__WEBPACK_IMPORTED_MODULE_7__.default);
function createData(coordSys, seriesModel, mpModel) {
  var coordDimsInfos;
  if (coordSys) {
    coordDimsInfos = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.map)(coordSys && coordSys.dimensions, function (coordDim) {
      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};
      // In map series data don't have lng and lat dimension. Fallback to same with coordSys
      return (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.extend)((0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.extend)({}, info), {
        name: coordDim,
        // DON'T use ordinalMeta to parse and collect ordinal.
        ordinalMeta: null
      });
    });
  } else {
    coordDimsInfos = [{
      name: 'value',
      type: 'float'
    }];
  }
  var mpData = new _data_SeriesData_js__WEBPACK_IMPORTED_MODULE_8__.default(coordDimsInfos, mpModel);
  var dataOpt = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.map)(mpModel.get('data'), (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.curry)(_markerHelper_js__WEBPACK_IMPORTED_MODULE_9__.dataTransform, seriesModel));
  if (coordSys) {
    dataOpt = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.filter)(dataOpt, (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.curry)(_markerHelper_js__WEBPACK_IMPORTED_MODULE_9__.dataFilter, coordSys));
  }
  var dimValueGetter = _markerHelper_js__WEBPACK_IMPORTED_MODULE_9__.createMarkerDimValueGetter(!!coordSys, coordDimsInfos);
  mpData.initData(dataOpt, null, dimValueGetter);
  return mpData;
}
/* harmony default export */ __webpack_exports__["default"] = (MarkPointView);

/***/ }),

/***/ "./node_modules/echarts/lib/component/marker/MarkerModel.js":
/*!******************************************************************!*\
  !*** ./node_modules/echarts/lib/component/marker/MarkerModel.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ "./node_modules/tslib/tslib.es6.js");
/* harmony import */ var zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zrender/lib/core/util.js */ "./node_modules/zrender/lib/core/util.js");
/* harmony import */ var zrender_lib_core_env_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zrender/lib/core/env.js */ "./node_modules/zrender/lib/core/env.js");
/* harmony import */ var _model_mixin_dataFormat_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../model/mixin/dataFormat.js */ "./node_modules/echarts/lib/model/mixin/dataFormat.js");
/* harmony import */ var _model_Component_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../model/Component.js */ "./node_modules/echarts/lib/model/Component.js");
/* harmony import */ var _util_model_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/model.js */ "./node_modules/echarts/lib/util/model.js");
/* harmony import */ var _tooltip_tooltipMarkup_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../tooltip/tooltipMarkup.js */ "./node_modules/echarts/lib/component/tooltip/tooltipMarkup.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/







function fillLabel(opt) {
  (0,_util_model_js__WEBPACK_IMPORTED_MODULE_0__.defaultEmphasis)(opt, 'label', ['show']);
}
// { [componentType]: MarkerModel }
var inner = (0,_util_model_js__WEBPACK_IMPORTED_MODULE_0__.makeInner)();
var MarkerModel = /** @class */function (_super) {
  (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__extends)(MarkerModel, _super);
  function MarkerModel() {
    var _this = _super !== null && _super.apply(this, arguments) || this;
    _this.type = MarkerModel.type;
    /**
     * If marker model is created by self from series
     */
    _this.createdBySelf = false;
    return _this;
  }
  /**
   * @overrite
   */
  MarkerModel.prototype.init = function (option, parentModel, ecModel) {
    if (true) {
      if (this.type === 'marker') {
        throw new Error('Marker component is abstract component. Use markLine, markPoint, markArea instead.');
      }
    }
    this.mergeDefaultAndTheme(option, ecModel);
    this._mergeOption(option, ecModel, false, true);
  };
  MarkerModel.prototype.isAnimationEnabled = function () {
    if (zrender_lib_core_env_js__WEBPACK_IMPORTED_MODULE_2__.default.node) {
      return false;
    }
    var hostSeries = this.__hostSeries;
    return this.getShallow('animation') && hostSeries && hostSeries.isAnimationEnabled();
  };
  /**
   * @overrite
   */
  MarkerModel.prototype.mergeOption = function (newOpt, ecModel) {
    this._mergeOption(newOpt, ecModel, false, false);
  };
  MarkerModel.prototype._mergeOption = function (newOpt, ecModel, createdBySelf, isInit) {
    var componentType = this.mainType;
    if (!createdBySelf) {
      ecModel.eachSeries(function (seriesModel) {
        // mainType can be markPoint, markLine, markArea
        var markerOpt = seriesModel.get(this.mainType, true);
        var markerModel = inner(seriesModel)[componentType];
        if (!markerOpt || !markerOpt.data) {
          inner(seriesModel)[componentType] = null;
          return;
        }
        if (!markerModel) {
          if (isInit) {
            // Default label emphasis `position` and `show`
            fillLabel(markerOpt);
          }
          zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_3__.each(markerOpt.data, function (item) {
            // FIXME Overwrite fillLabel method ?
            if (item instanceof Array) {
              fillLabel(item[0]);
              fillLabel(item[1]);
            } else {
              fillLabel(item);
            }
          });
          markerModel = this.createMarkerModelFromSeries(markerOpt, this, ecModel);
          // markerModel = new ImplementedMarkerModel(
          //     markerOpt, this, ecModel
          // );
          zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_3__.extend(markerModel, {
            mainType: this.mainType,
            // Use the same series index and name
            seriesIndex: seriesModel.seriesIndex,
            name: seriesModel.name,
            createdBySelf: true
          });
          markerModel.__hostSeries = seriesModel;
        } else {
          markerModel._mergeOption(markerOpt, ecModel, true);
        }
        inner(seriesModel)[componentType] = markerModel;
      }, this);
    }
  };
  MarkerModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {
    var data = this.getData();
    var value = this.getRawValue(dataIndex);
    var itemName = data.getName(dataIndex);
    return (0,_tooltip_tooltipMarkup_js__WEBPACK_IMPORTED_MODULE_4__.createTooltipMarkup)('section', {
      header: this.name,
      blocks: [(0,_tooltip_tooltipMarkup_js__WEBPACK_IMPORTED_MODULE_4__.createTooltipMarkup)('nameValue', {
        name: itemName,
        value: value,
        noName: !itemName,
        noValue: value == null
      })]
    });
  };
  MarkerModel.prototype.getData = function () {
    return this._data;
  };
  MarkerModel.prototype.setData = function (data) {
    this._data = data;
  };
  MarkerModel.prototype.getDataParams = function (dataIndex, dataType) {
    var params = _model_mixin_dataFormat_js__WEBPACK_IMPORTED_MODULE_5__.DataFormatMixin.prototype.getDataParams.call(this, dataIndex, dataType);
    var hostSeries = this.__hostSeries;
    if (hostSeries) {
      params.seriesId = hostSeries.id;
      params.seriesName = hostSeries.name;
      params.seriesType = hostSeries.subType;
    }
    return params;
  };
  MarkerModel.getMarkerModelFromSeries = function (seriesModel,
  // Support three types of markers. Strict check.
  componentType) {
    return inner(seriesModel)[componentType];
  };
  MarkerModel.type = 'marker';
  MarkerModel.dependencies = ['series', 'grid', 'polar', 'geo'];
  return MarkerModel;
}(_model_Component_js__WEBPACK_IMPORTED_MODULE_6__.default);
zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_3__.mixin(MarkerModel, _model_mixin_dataFormat_js__WEBPACK_IMPORTED_MODULE_5__.DataFormatMixin.prototype);
/* harmony default export */ __webpack_exports__["default"] = (MarkerModel);

/***/ }),

/***/ "./node_modules/echarts/lib/component/marker/MarkerView.js":
/*!*****************************************************************!*\
  !*** ./node_modules/echarts/lib/component/marker/MarkerView.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ "./node_modules/tslib/tslib.es6.js");
/* harmony import */ var _view_Component_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../view/Component.js */ "./node_modules/echarts/lib/view/Component.js");
/* harmony import */ var zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zrender/lib/core/util.js */ "./node_modules/zrender/lib/core/util.js");
/* harmony import */ var _MarkerModel_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MarkerModel.js */ "./node_modules/echarts/lib/component/marker/MarkerModel.js");
/* harmony import */ var _util_model_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/model.js */ "./node_modules/echarts/lib/util/model.js");
/* harmony import */ var _util_states_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../util/states.js */ "./node_modules/echarts/lib/util/states.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/






var inner = (0,_util_model_js__WEBPACK_IMPORTED_MODULE_0__.makeInner)();
var MarkerView = /** @class */function (_super) {
  (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__extends)(MarkerView, _super);
  function MarkerView() {
    var _this = _super !== null && _super.apply(this, arguments) || this;
    _this.type = MarkerView.type;
    return _this;
  }
  MarkerView.prototype.init = function () {
    this.markerGroupMap = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.createHashMap)();
  };
  MarkerView.prototype.render = function (markerModel, ecModel, api) {
    var _this = this;
    var markerGroupMap = this.markerGroupMap;
    markerGroupMap.each(function (item) {
      inner(item).keep = false;
    });
    ecModel.eachSeries(function (seriesModel) {
      var markerModel = _MarkerModel_js__WEBPACK_IMPORTED_MODULE_3__.default.getMarkerModelFromSeries(seriesModel, _this.type);
      markerModel && _this.renderSeries(seriesModel, markerModel, ecModel, api);
    });
    markerGroupMap.each(function (item) {
      !inner(item).keep && _this.group.remove(item.group);
    });
  };
  MarkerView.prototype.markKeep = function (drawGroup) {
    inner(drawGroup).keep = true;
  };
  MarkerView.prototype.toggleBlurSeries = function (seriesModelList, isBlur) {
    var _this = this;
    (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.each)(seriesModelList, function (seriesModel) {
      var markerModel = _MarkerModel_js__WEBPACK_IMPORTED_MODULE_3__.default.getMarkerModelFromSeries(seriesModel, _this.type);
      if (markerModel) {
        var data = markerModel.getData();
        data.eachItemGraphicEl(function (el) {
          if (el) {
            isBlur ? (0,_util_states_js__WEBPACK_IMPORTED_MODULE_4__.enterBlur)(el) : (0,_util_states_js__WEBPACK_IMPORTED_MODULE_4__.leaveBlur)(el);
          }
        });
      }
    });
  };
  MarkerView.type = 'marker';
  return MarkerView;
}(_view_Component_js__WEBPACK_IMPORTED_MODULE_5__.default);
/* harmony default export */ __webpack_exports__["default"] = (MarkerView);

/***/ }),

/***/ "./node_modules/echarts/lib/component/marker/checkMarkerInSeries.js":
/*!**************************************************************************!*\
  !*** ./node_modules/echarts/lib/component/marker/checkMarkerInSeries.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ checkMarkerInSeries; }
/* harmony export */ });
/* harmony import */ var zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zrender/lib/core/util.js */ "./node_modules/zrender/lib/core/util.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/

function checkMarkerInSeries(seriesOpts, markerType) {
  if (!seriesOpts) {
    return false;
  }
  var seriesOptArr = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(seriesOpts) ? seriesOpts : [seriesOpts];
  for (var idx = 0; idx < seriesOptArr.length; idx++) {
    if (seriesOptArr[idx] && seriesOptArr[idx][markerType]) {
      return true;
    }
  }
  return false;
}

/***/ }),

/***/ "./node_modules/echarts/lib/component/marker/installMarkPoint.js":
/*!***********************************************************************!*\
  !*** ./node_modules/echarts/lib/component/marker/installMarkPoint.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "install": function() { return /* binding */ install; }
/* harmony export */ });
/* harmony import */ var _checkMarkerInSeries_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./checkMarkerInSeries.js */ "./node_modules/echarts/lib/component/marker/checkMarkerInSeries.js");
/* harmony import */ var _MarkPointModel_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./MarkPointModel.js */ "./node_modules/echarts/lib/component/marker/MarkPointModel.js");
/* harmony import */ var _MarkPointView_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MarkPointView.js */ "./node_modules/echarts/lib/component/marker/MarkPointView.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */




function install(registers) {
  registers.registerComponentModel(_MarkPointModel_js__WEBPACK_IMPORTED_MODULE_0__.default);
  registers.registerComponentView(_MarkPointView_js__WEBPACK_IMPORTED_MODULE_1__.default);
  registers.registerPreprocessor(function (opt) {
    if ((0,_checkMarkerInSeries_js__WEBPACK_IMPORTED_MODULE_2__.default)(opt.series, 'markPoint')) {
      // Make sure markPoint component is enabled
      opt.markPoint = opt.markPoint || {};
    }
  });
}

/***/ }),

/***/ "./node_modules/echarts/lib/component/marker/markerHelper.js":
/*!*******************************************************************!*\
  !*** ./node_modules/echarts/lib/component/marker/markerHelper.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "dataTransform": function() { return /* binding */ dataTransform; },
/* harmony export */   "getAxisInfo": function() { return /* binding */ getAxisInfo; },
/* harmony export */   "dataFilter": function() { return /* binding */ dataFilter; },
/* harmony export */   "zoneFilter": function() { return /* binding */ zoneFilter; },
/* harmony export */   "createMarkerDimValueGetter": function() { return /* binding */ createMarkerDimValueGetter; },
/* harmony export */   "numCalculate": function() { return /* binding */ numCalculate; }
/* harmony export */ });
/* harmony import */ var _util_number_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/number.js */ "./node_modules/echarts/lib/util/number.js");
/* harmony import */ var _data_helper_dataStackHelper_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../data/helper/dataStackHelper.js */ "./node_modules/echarts/lib/data/helper/dataStackHelper.js");
/* harmony import */ var zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zrender/lib/core/util.js */ "./node_modules/zrender/lib/core/util.js");
/* harmony import */ var _data_helper_dataValueHelper_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../data/helper/dataValueHelper.js */ "./node_modules/echarts/lib/data/helper/dataValueHelper.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/




function hasXOrY(item) {
  return !(isNaN(parseFloat(item.x)) && isNaN(parseFloat(item.y)));
}
function hasXAndY(item) {
  return !isNaN(parseFloat(item.x)) && !isNaN(parseFloat(item.y));
}
function markerTypeCalculatorWithExtent(markerType, data, otherDataDim, targetDataDim, otherCoordIndex, targetCoordIndex) {
  var coordArr = [];
  var stacked = (0,_data_helper_dataStackHelper_js__WEBPACK_IMPORTED_MODULE_0__.isDimensionStacked)(data, targetDataDim /* , otherDataDim */);
  var calcDataDim = stacked ? data.getCalculationInfo('stackResultDimension') : targetDataDim;
  var value = numCalculate(data, calcDataDim, markerType);
  var dataIndex = data.indicesOfNearest(calcDataDim, value)[0];
  coordArr[otherCoordIndex] = data.get(otherDataDim, dataIndex);
  coordArr[targetCoordIndex] = data.get(calcDataDim, dataIndex);
  var coordArrValue = data.get(targetDataDim, dataIndex);
  // Make it simple, do not visit all stacked value to count precision.
  var precision = _util_number_js__WEBPACK_IMPORTED_MODULE_1__.getPrecision(data.get(targetDataDim, dataIndex));
  precision = Math.min(precision, 20);
  if (precision >= 0) {
    coordArr[targetCoordIndex] = +coordArr[targetCoordIndex].toFixed(precision);
  }
  return [coordArr, coordArrValue];
}
// TODO Specified percent
var markerTypeCalculator = {
  min: (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.curry)(markerTypeCalculatorWithExtent, 'min'),
  max: (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.curry)(markerTypeCalculatorWithExtent, 'max'),
  average: (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.curry)(markerTypeCalculatorWithExtent, 'average'),
  median: (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.curry)(markerTypeCalculatorWithExtent, 'median')
};
/**
 * Transform markPoint data item to format used in List by do the following
 * 1. Calculate statistic like `max`, `min`, `average`
 * 2. Convert `item.xAxis`, `item.yAxis` to `item.coord` array
 */
function dataTransform(seriesModel, item) {
  if (!item) {
    return;
  }
  var data = seriesModel.getData();
  var coordSys = seriesModel.coordinateSystem;
  var dims = coordSys && coordSys.dimensions;
  // 1. If not specify the position with pixel directly
  // 2. If `coord` is not a data array. Which uses `xAxis`,
  // `yAxis` to specify the coord on each dimension
  // parseFloat first because item.x and item.y can be percent string like '20%'
  if (!hasXAndY(item) && !(0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.isArray)(item.coord) && (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.isArray)(dims)) {
    var axisInfo = getAxisInfo(item, data, coordSys, seriesModel);
    // Clone the option
    // Transform the properties xAxis, yAxis, radiusAxis, angleAxis, geoCoord to value
    item = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.clone)(item);
    if (item.type && markerTypeCalculator[item.type] && axisInfo.baseAxis && axisInfo.valueAxis) {
      var otherCoordIndex = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.indexOf)(dims, axisInfo.baseAxis.dim);
      var targetCoordIndex = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.indexOf)(dims, axisInfo.valueAxis.dim);
      var coordInfo = markerTypeCalculator[item.type](data, axisInfo.baseDataDim, axisInfo.valueDataDim, otherCoordIndex, targetCoordIndex);
      item.coord = coordInfo[0];
      // Force to use the value of calculated value.
      // let item use the value without stack.
      item.value = coordInfo[1];
    } else {
      // FIXME Only has one of xAxis and yAxis.
      item.coord = [item.xAxis != null ? item.xAxis : item.radiusAxis, item.yAxis != null ? item.yAxis : item.angleAxis];
    }
  }
  // x y is provided
  if (item.coord == null || !(0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_2__.isArray)(dims)) {
    item.coord = [];
  } else {
    // Each coord support max, min, average
    var coord = item.coord;
    for (var i = 0; i < 2; i++) {
      if (markerTypeCalculator[coord[i]]) {
        coord[i] = numCalculate(data, data.mapDimension(dims[i]), coord[i]);
      }
    }
  }
  return item;
}
function getAxisInfo(item, data, coordSys, seriesModel) {
  var ret = {};
  if (item.valueIndex != null || item.valueDim != null) {
    ret.valueDataDim = item.valueIndex != null ? data.getDimension(item.valueIndex) : item.valueDim;
    ret.valueAxis = coordSys.getAxis(dataDimToCoordDim(seriesModel, ret.valueDataDim));
    ret.baseAxis = coordSys.getOtherAxis(ret.valueAxis);
    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);
  } else {
    ret.baseAxis = seriesModel.getBaseAxis();
    ret.valueAxis = coordSys.getOtherAxis(ret.baseAxis);
    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);
    ret.valueDataDim = data.mapDimension(ret.valueAxis.dim);
  }
  return ret;
}
function dataDimToCoordDim(seriesModel, dataDim) {
  var dimItem = seriesModel.getData().getDimensionInfo(dataDim);
  return dimItem && dimItem.coordDim;
}
/**
 * Filter data which is out of coordinateSystem range
 * [dataFilter description]
 */
function dataFilter(
// Currently only polar and cartesian has containData.
coordSys, item) {
  // Always return true if there is no coordSys
  return coordSys && coordSys.containData && item.coord && !hasXOrY(item) ? coordSys.containData(item.coord) : true;
}
function zoneFilter(
// Currently only polar and cartesian has containData.
coordSys, item1, item2) {
  // Always return true if there is no coordSys
  return coordSys && coordSys.containZone && item1.coord && item2.coord && !hasXOrY(item1) && !hasXOrY(item2) ? coordSys.containZone(item1.coord, item2.coord) : true;
}
function createMarkerDimValueGetter(inCoordSys, dims) {
  return inCoordSys ? function (item, dimName, dataIndex, dimIndex) {
    var rawVal = dimIndex < 2
    // x, y, radius, angle
    ? item.coord && item.coord[dimIndex] : item.value;
    return (0,_data_helper_dataValueHelper_js__WEBPACK_IMPORTED_MODULE_3__.parseDataValue)(rawVal, dims[dimIndex]);
  } : function (item, dimName, dataIndex, dimIndex) {
    return (0,_data_helper_dataValueHelper_js__WEBPACK_IMPORTED_MODULE_3__.parseDataValue)(item.value, dims[dimIndex]);
  };
}
function numCalculate(data, valueDataDim, type) {
  if (type === 'average') {
    var sum_1 = 0;
    var count_1 = 0;
    data.each(valueDataDim, function (val, idx) {
      if (!isNaN(val)) {
        sum_1 += val;
        count_1++;
      }
    });
    return sum_1 / count_1;
  } else if (type === 'median') {
    return data.getMedian(valueDataDim);
  } else {
    // max & min
    return data.getDataExtent(valueDataDim)[type === 'max' ? 1 : 0];
  }
}

/***/ })

}]);