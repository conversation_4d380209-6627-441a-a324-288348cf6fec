(self["webpackChunk"] = self["webpackChunk"] || []).push([["mf-dep_vendors-node_modules_echarts_lib_export_core_js"],{

/***/ "./node_modules/echarts/lib/export/api.js":
/*!************************************************!*\
  !*** ./node_modules/echarts/lib/export/api.js ***!
  \************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "zrender": function() { return /* reexport module object */ zrender_lib_zrender_js__WEBPACK_IMPORTED_MODULE_0__; },
/* harmony export */   "matrix": function() { return /* reexport module object */ zrender_lib_core_matrix_js__WEBPACK_IMPORTED_MODULE_1__; },
/* harmony export */   "vector": function() { return /* reexport module object */ zrender_lib_core_vector_js__WEBPACK_IMPORTED_MODULE_2__; },
/* harmony export */   "zrUtil": function() { return /* reexport module object */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_3__; },
/* harmony export */   "color": function() { return /* reexport module object */ zrender_lib_tool_color_js__WEBPACK_IMPORTED_MODULE_4__; },
/* harmony export */   "throttle": function() { return /* reexport safe */ _util_throttle_js__WEBPACK_IMPORTED_MODULE_5__.throttle; },
/* harmony export */   "helper": function() { return /* reexport module object */ _api_helper_js__WEBPACK_IMPORTED_MODULE_6__; },
/* harmony export */   "use": function() { return /* reexport safe */ _extension_js__WEBPACK_IMPORTED_MODULE_7__.use; },
/* harmony export */   "setPlatformAPI": function() { return /* reexport safe */ zrender_lib_core_platform_js__WEBPACK_IMPORTED_MODULE_8__.setPlatformAPI; },
/* harmony export */   "parseGeoJSON": function() { return /* reexport safe */ _coord_geo_parseGeoJson_js__WEBPACK_IMPORTED_MODULE_9__.default; },
/* harmony export */   "parseGeoJson": function() { return /* reexport safe */ _coord_geo_parseGeoJson_js__WEBPACK_IMPORTED_MODULE_9__.default; },
/* harmony export */   "number": function() { return /* reexport module object */ _api_number_js__WEBPACK_IMPORTED_MODULE_10__; },
/* harmony export */   "time": function() { return /* reexport module object */ _api_time_js__WEBPACK_IMPORTED_MODULE_11__; },
/* harmony export */   "graphic": function() { return /* reexport module object */ _api_graphic_js__WEBPACK_IMPORTED_MODULE_12__; },
/* harmony export */   "format": function() { return /* reexport module object */ _api_format_js__WEBPACK_IMPORTED_MODULE_13__; },
/* harmony export */   "util": function() { return /* reexport module object */ _api_util_js__WEBPACK_IMPORTED_MODULE_14__; },
/* harmony export */   "env": function() { return /* reexport safe */ zrender_lib_core_env_js__WEBPACK_IMPORTED_MODULE_15__.default; },
/* harmony export */   "List": function() { return /* reexport safe */ _data_SeriesData_js__WEBPACK_IMPORTED_MODULE_16__.default; },
/* harmony export */   "Model": function() { return /* reexport safe */ _model_Model_js__WEBPACK_IMPORTED_MODULE_17__.default; },
/* harmony export */   "Axis": function() { return /* reexport safe */ _coord_Axis_js__WEBPACK_IMPORTED_MODULE_18__.default; },
/* harmony export */   "ComponentModel": function() { return /* reexport safe */ _model_Component_js__WEBPACK_IMPORTED_MODULE_19__.default; },
/* harmony export */   "ComponentView": function() { return /* reexport safe */ _view_Component_js__WEBPACK_IMPORTED_MODULE_20__.default; },
/* harmony export */   "SeriesModel": function() { return /* reexport safe */ _model_Series_js__WEBPACK_IMPORTED_MODULE_21__.default; },
/* harmony export */   "ChartView": function() { return /* reexport safe */ _view_Chart_js__WEBPACK_IMPORTED_MODULE_22__.default; },
/* harmony export */   "innerDrawElementOnCanvas": function() { return /* reexport safe */ zrender_lib_canvas_graphic_js__WEBPACK_IMPORTED_MODULE_23__.brushSingle; },
/* harmony export */   "extendComponentModel": function() { return /* binding */ extendComponentModel; },
/* harmony export */   "extendComponentView": function() { return /* binding */ extendComponentView; },
/* harmony export */   "extendSeriesModel": function() { return /* binding */ extendSeriesModel; },
/* harmony export */   "extendChartView": function() { return /* binding */ extendChartView; }
/* harmony export */ });
/* harmony import */ var _model_Component_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../model/Component.js */ "./node_modules/echarts/lib/model/Component.js");
/* harmony import */ var _view_Component_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ../view/Component.js */ "./node_modules/echarts/lib/view/Component.js");
/* harmony import */ var _model_Series_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ../model/Series.js */ "./node_modules/echarts/lib/model/Series.js");
/* harmony import */ var _view_Chart_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ../view/Chart.js */ "./node_modules/echarts/lib/view/Chart.js");
/* harmony import */ var _data_SeriesData_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../data/SeriesData.js */ "./node_modules/echarts/lib/data/SeriesData.js");
/* harmony import */ var zrender_lib_zrender_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zrender/lib/zrender.js */ "./node_modules/zrender/lib/zrender.js");
/* harmony import */ var zrender_lib_core_matrix_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zrender/lib/core/matrix.js */ "./node_modules/zrender/lib/core/matrix.js");
/* harmony import */ var zrender_lib_core_vector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zrender/lib/core/vector.js */ "./node_modules/zrender/lib/core/vector.js");
/* harmony import */ var zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zrender/lib/core/util.js */ "./node_modules/zrender/lib/core/util.js");
/* harmony import */ var zrender_lib_tool_color_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zrender/lib/tool/color.js */ "./node_modules/zrender/lib/tool/color.js");
/* harmony import */ var _util_throttle_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/throttle.js */ "./node_modules/echarts/lib/util/throttle.js");
/* harmony import */ var _api_helper_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./api/helper.js */ "./node_modules/echarts/lib/export/api/helper.js");
/* harmony import */ var _extension_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../extension.js */ "./node_modules/echarts/lib/extension.js");
/* harmony import */ var zrender_lib_core_platform_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! zrender/lib/core/platform.js */ "./node_modules/zrender/lib/core/platform.js");
/* harmony import */ var _coord_geo_parseGeoJson_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../coord/geo/parseGeoJson.js */ "./node_modules/echarts/lib/coord/geo/parseGeoJson.js");
/* harmony import */ var _api_number_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./api/number.js */ "./node_modules/echarts/lib/export/api/number.js");
/* harmony import */ var _api_time_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./api/time.js */ "./node_modules/echarts/lib/export/api/time.js");
/* harmony import */ var _api_graphic_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./api/graphic.js */ "./node_modules/echarts/lib/export/api/graphic.js");
/* harmony import */ var _api_format_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./api/format.js */ "./node_modules/echarts/lib/export/api/format.js");
/* harmony import */ var _api_util_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./api/util.js */ "./node_modules/echarts/lib/export/api/util.js");
/* harmony import */ var zrender_lib_core_env_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! zrender/lib/core/env.js */ "./node_modules/zrender/lib/core/env.js");
/* harmony import */ var _model_Model_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../model/Model.js */ "./node_modules/echarts/lib/model/Model.js");
/* harmony import */ var _coord_Axis_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../coord/Axis.js */ "./node_modules/echarts/lib/coord/Axis.js");
/* harmony import */ var zrender_lib_canvas_graphic_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! zrender/lib/canvas/graphic.js */ "./node_modules/zrender/lib/canvas/graphic.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/
// These APIs are for more advanced usages
// For example extend charts and components, creating graphic elements, formatting.




















// --------------------- Helper Methods ---------------------













// --------------------- Export for Extension Usage ---------------------
// export {SeriesData};
 // TODO: Compatitable with exists echarts-gl code



// Only for GL

// --------------------- Deprecated Extension Methods ---------------------
// Should use `ComponentModel.extend` or `class XXXX extend ComponentModel` to create class.
// Then use `registerComponentModel` in `install` parameter when `use` this extension. For example:
// class Bar3DModel extends ComponentModel {}
// export function install(registers) { registers.registerComponentModel(Bar3DModel); }
// echarts.use(install);
function extendComponentModel(proto) {
  var Model = _model_Component_js__WEBPACK_IMPORTED_MODULE_19__.default.extend(proto);
  _model_Component_js__WEBPACK_IMPORTED_MODULE_19__.default.registerClass(Model);
  return Model;
}
function extendComponentView(proto) {
  var View = _view_Component_js__WEBPACK_IMPORTED_MODULE_20__.default.extend(proto);
  _view_Component_js__WEBPACK_IMPORTED_MODULE_20__.default.registerClass(View);
  return View;
}
function extendSeriesModel(proto) {
  var Model = _model_Series_js__WEBPACK_IMPORTED_MODULE_21__.default.extend(proto);
  _model_Series_js__WEBPACK_IMPORTED_MODULE_21__.default.registerClass(Model);
  return Model;
}
function extendChartView(proto) {
  var View = _view_Chart_js__WEBPACK_IMPORTED_MODULE_22__.default.extend(proto);
  _view_Chart_js__WEBPACK_IMPORTED_MODULE_22__.default.registerClass(View);
  return View;
}

/***/ }),

/***/ "./node_modules/echarts/lib/export/api/format.js":
/*!*******************************************************!*\
  !*** ./node_modules/echarts/lib/export/api/format.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "addCommas": function() { return /* reexport safe */ _util_format_js__WEBPACK_IMPORTED_MODULE_0__.addCommas; },
/* harmony export */   "toCamelCase": function() { return /* reexport safe */ _util_format_js__WEBPACK_IMPORTED_MODULE_0__.toCamelCase; },
/* harmony export */   "normalizeCssArray": function() { return /* reexport safe */ _util_format_js__WEBPACK_IMPORTED_MODULE_0__.normalizeCssArray; },
/* harmony export */   "encodeHTML": function() { return /* reexport safe */ _util_format_js__WEBPACK_IMPORTED_MODULE_1__.encodeHTML; },
/* harmony export */   "formatTpl": function() { return /* reexport safe */ _util_format_js__WEBPACK_IMPORTED_MODULE_0__.formatTpl; },
/* harmony export */   "getTooltipMarker": function() { return /* reexport safe */ _util_format_js__WEBPACK_IMPORTED_MODULE_0__.getTooltipMarker; },
/* harmony export */   "formatTime": function() { return /* reexport safe */ _util_format_js__WEBPACK_IMPORTED_MODULE_0__.formatTime; },
/* harmony export */   "capitalFirst": function() { return /* reexport safe */ _util_format_js__WEBPACK_IMPORTED_MODULE_0__.capitalFirst; },
/* harmony export */   "truncateText": function() { return /* reexport safe */ _util_format_js__WEBPACK_IMPORTED_MODULE_2__.truncateText; },
/* harmony export */   "getTextRect": function() { return /* reexport safe */ _util_format_js__WEBPACK_IMPORTED_MODULE_3__.getTextRect; }
/* harmony export */ });
/* harmony import */ var _util_format_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/format.js */ "./node_modules/echarts/lib/util/format.js");
/* harmony import */ var _util_format_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/format.js */ "./node_modules/zrender/lib/core/dom.js");
/* harmony import */ var _util_format_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../util/format.js */ "./node_modules/zrender/lib/graphic/helper/parseText.js");
/* harmony import */ var _util_format_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../util/format.js */ "./node_modules/echarts/lib/legacy/getTextRect.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/***/ }),

/***/ "./node_modules/echarts/lib/export/api/graphic.js":
/*!********************************************************!*\
  !*** ./node_modules/echarts/lib/export/api/graphic.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "extendShape": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.extendShape; },
/* harmony export */   "extendPath": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.extendPath; },
/* harmony export */   "makePath": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.makePath; },
/* harmony export */   "makeImage": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.makeImage; },
/* harmony export */   "mergePath": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.mergePath; },
/* harmony export */   "resizePath": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.resizePath; },
/* harmony export */   "createIcon": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.createIcon; },
/* harmony export */   "updateProps": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_1__.updateProps; },
/* harmony export */   "initProps": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_1__.initProps; },
/* harmony export */   "getTransform": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.getTransform; },
/* harmony export */   "clipPointsByRect": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.clipPointsByRect; },
/* harmony export */   "clipRectByRect": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.clipRectByRect; },
/* harmony export */   "registerShape": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.registerShape; },
/* harmony export */   "getShapeClass": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__.getShapeClass; },
/* harmony export */   "Group": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_2__.default; },
/* harmony export */   "Image": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_3__.default; },
/* harmony export */   "Text": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_4__.default; },
/* harmony export */   "Circle": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_5__.default; },
/* harmony export */   "Ellipse": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_6__.default; },
/* harmony export */   "Sector": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_7__.default; },
/* harmony export */   "Ring": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_8__.default; },
/* harmony export */   "Polygon": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_9__.default; },
/* harmony export */   "Polyline": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_10__.default; },
/* harmony export */   "Rect": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_11__.default; },
/* harmony export */   "Line": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_12__.default; },
/* harmony export */   "BezierCurve": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_13__.default; },
/* harmony export */   "Arc": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_14__.default; },
/* harmony export */   "IncrementalDisplayable": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_15__.default; },
/* harmony export */   "CompoundPath": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_16__.default; },
/* harmony export */   "LinearGradient": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_17__.default; },
/* harmony export */   "RadialGradient": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_18__.default; },
/* harmony export */   "BoundingRect": function() { return /* reexport safe */ _util_graphic_js__WEBPACK_IMPORTED_MODULE_19__.default; }
/* harmony export */ });
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/echarts/lib/util/graphic.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/echarts/lib/animation/basicTransition.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/Group.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/Image.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/Text.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/shape/Circle.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/shape/Ellipse.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/shape/Sector.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/shape/Ring.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/shape/Polygon.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/shape/Polyline.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/shape/Rect.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/shape/Line.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/shape/BezierCurve.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/shape/Arc.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/IncrementalDisplayable.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/CompoundPath.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/LinearGradient.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/graphic/RadialGradient.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../../util/graphic.js */ "./node_modules/zrender/lib/core/BoundingRect.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/***/ }),

/***/ "./node_modules/echarts/lib/export/api/helper.js":
/*!*******************************************************!*\
  !*** ./node_modules/echarts/lib/export/api/helper.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "createList": function() { return /* binding */ createList; },
/* harmony export */   "getLayoutRect": function() { return /* reexport safe */ _util_layout_js__WEBPACK_IMPORTED_MODULE_1__.getLayoutRect; },
/* harmony export */   "createDimensions": function() { return /* reexport safe */ _data_helper_createDimensions_js__WEBPACK_IMPORTED_MODULE_2__.createDimensions; },
/* harmony export */   "dataStack": function() { return /* binding */ dataStack; },
/* harmony export */   "createSymbol": function() { return /* reexport safe */ _util_symbol_js__WEBPACK_IMPORTED_MODULE_4__.createSymbol; },
/* harmony export */   "createScale": function() { return /* binding */ createScale; },
/* harmony export */   "mixinAxisModelCommonMethods": function() { return /* binding */ mixinAxisModelCommonMethods; },
/* harmony export */   "getECData": function() { return /* reexport safe */ _util_innerStore_js__WEBPACK_IMPORTED_MODULE_9__.getECData; },
/* harmony export */   "enableHoverEmphasis": function() { return /* reexport safe */ _util_states_js__WEBPACK_IMPORTED_MODULE_10__.enableHoverEmphasis; },
/* harmony export */   "createTextStyle": function() { return /* binding */ createTextStyle; }
/* harmony export */ });
/* harmony import */ var zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zrender/lib/core/util.js */ "./node_modules/zrender/lib/core/util.js");
/* harmony import */ var _chart_helper_createSeriesData_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../chart/helper/createSeriesData.js */ "./node_modules/echarts/lib/chart/helper/createSeriesData.js");
/* harmony import */ var _coord_axisHelper_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../coord/axisHelper.js */ "./node_modules/echarts/lib/coord/axisHelper.js");
/* harmony import */ var _coord_axisModelCommonMixin_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../coord/axisModelCommonMixin.js */ "./node_modules/echarts/lib/coord/axisModelCommonMixin.js");
/* harmony import */ var _model_Model_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../model/Model.js */ "./node_modules/echarts/lib/model/Model.js");
/* harmony import */ var _util_layout_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/layout.js */ "./node_modules/echarts/lib/util/layout.js");
/* harmony import */ var _data_helper_dataStackHelper_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../data/helper/dataStackHelper.js */ "./node_modules/echarts/lib/data/helper/dataStackHelper.js");
/* harmony import */ var _util_innerStore_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../util/innerStore.js */ "./node_modules/echarts/lib/util/innerStore.js");
/* harmony import */ var _label_labelStyle_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../label/labelStyle.js */ "./node_modules/echarts/lib/label/labelStyle.js");
/* harmony import */ var _data_helper_createDimensions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../data/helper/createDimensions.js */ "./node_modules/echarts/lib/data/helper/createDimensions.js");
/* harmony import */ var _util_symbol_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../util/symbol.js */ "./node_modules/echarts/lib/util/symbol.js");
/* harmony import */ var _util_states_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../util/states.js */ "./node_modules/echarts/lib/util/states.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/
/**
 * This module exposes helper functions for developing extensions.
 */


// import createGraphFromNodeEdge from './chart/helper/createGraphFromNodeEdge.js';







/**
 * Create a multi dimension List structure from seriesModel.
 */
function createList(seriesModel) {
  return (0,_chart_helper_createSeriesData_js__WEBPACK_IMPORTED_MODULE_0__.default)(null, seriesModel);
}
// export function createGraph(seriesModel) {
//     let nodes = seriesModel.get('data');
//     let links = seriesModel.get('links');
//     return createGraphFromNodeEdge(nodes, links, seriesModel);
// }


var dataStack = {
  isDimensionStacked: _data_helper_dataStackHelper_js__WEBPACK_IMPORTED_MODULE_3__.isDimensionStacked,
  enableDataStack: _data_helper_dataStackHelper_js__WEBPACK_IMPORTED_MODULE_3__.enableDataStack,
  getStackedDimension: _data_helper_dataStackHelper_js__WEBPACK_IMPORTED_MODULE_3__.getStackedDimension
};
/**
 * Create a symbol element with given symbol configuration: shape, x, y, width, height, color
 * @param {string} symbolDesc
 * @param {number} x
 * @param {number} y
 * @param {number} w
 * @param {number} h
 * @param {string} color
 */

/**
 * Create scale
 * @param {Array.<number>} dataExtent
 * @param {Object|module:echarts/Model} option If `optoin.type`
 *        is secified, it can only be `'value'` currently.
 */
function createScale(dataExtent, option) {
  var axisModel = option;
  if (!(option instanceof _model_Model_js__WEBPACK_IMPORTED_MODULE_5__.default)) {
    axisModel = new _model_Model_js__WEBPACK_IMPORTED_MODULE_5__.default(option);
    // FIXME
    // Currently AxisModelCommonMixin has nothing to do with the
    // the requirements of `axisHelper.createScaleByModel`. For
    // example the methods `getCategories` and `getOrdinalMeta`
    // are required for `'category'` axis, and ecModel is required
    // for `'time'` axis. But occasionally echarts-gl happened
    // to only use `'value'` axis.
    // zrUtil.mixin(axisModel, AxisModelCommonMixin);
  }
  var scale = _coord_axisHelper_js__WEBPACK_IMPORTED_MODULE_6__.createScaleByModel(axisModel);
  scale.setExtent(dataExtent[0], dataExtent[1]);
  _coord_axisHelper_js__WEBPACK_IMPORTED_MODULE_6__.niceScaleExtent(scale, axisModel);
  return scale;
}
/**
 * Mixin common methods to axis model,
 *
 * Include methods
 * `getFormattedLabels() => Array.<string>`
 * `getCategories() => Array.<string>`
 * `getMin(origin: boolean) => number`
 * `getMax(origin: boolean) => number`
 * `getNeedCrossZero() => boolean`
 */
function mixinAxisModelCommonMethods(Model) {
  zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_7__.mixin(Model, _coord_axisModelCommonMixin_js__WEBPACK_IMPORTED_MODULE_8__.AxisModelCommonMixin);
}


function createTextStyle(textStyleModel, opts) {
  opts = opts || {};
  return (0,_label_labelStyle_js__WEBPACK_IMPORTED_MODULE_11__.createTextStyle)(textStyleModel, null, null, opts.state !== 'normal');
}

/***/ }),

/***/ "./node_modules/echarts/lib/export/api/number.js":
/*!*******************************************************!*\
  !*** ./node_modules/echarts/lib/export/api/number.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "linearMap": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.linearMap; },
/* harmony export */   "round": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.round; },
/* harmony export */   "asc": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.asc; },
/* harmony export */   "getPrecision": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.getPrecision; },
/* harmony export */   "getPrecisionSafe": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.getPrecisionSafe; },
/* harmony export */   "getPixelPrecision": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.getPixelPrecision; },
/* harmony export */   "getPercentWithPrecision": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.getPercentWithPrecision; },
/* harmony export */   "MAX_SAFE_INTEGER": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.MAX_SAFE_INTEGER; },
/* harmony export */   "remRadian": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.remRadian; },
/* harmony export */   "isRadianAroundZero": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.isRadianAroundZero; },
/* harmony export */   "parseDate": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.parseDate; },
/* harmony export */   "quantity": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.quantity; },
/* harmony export */   "quantityExponent": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.quantityExponent; },
/* harmony export */   "nice": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.nice; },
/* harmony export */   "quantile": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.quantile; },
/* harmony export */   "reformIntervals": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.reformIntervals; },
/* harmony export */   "isNumeric": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.isNumeric; },
/* harmony export */   "numericToNumber": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.numericToNumber; }
/* harmony export */ });
/* harmony import */ var _util_number_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/number.js */ "./node_modules/echarts/lib/util/number.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/***/ }),

/***/ "./node_modules/echarts/lib/export/api/time.js":
/*!*****************************************************!*\
  !*** ./node_modules/echarts/lib/export/api/time.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "parse": function() { return /* reexport safe */ _util_number_js__WEBPACK_IMPORTED_MODULE_0__.parseDate; },
/* harmony export */   "format": function() { return /* reexport safe */ _util_time_js__WEBPACK_IMPORTED_MODULE_1__.format; }
/* harmony export */ });
/* harmony import */ var _util_number_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/number.js */ "./node_modules/echarts/lib/util/number.js");
/* harmony import */ var _util_time_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/time.js */ "./node_modules/echarts/lib/util/time.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/



/***/ }),

/***/ "./node_modules/echarts/lib/export/api/util.js":
/*!*****************************************************!*\
  !*** ./node_modules/echarts/lib/export/api/util.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "map": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.map; },
/* harmony export */   "each": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.each; },
/* harmony export */   "indexOf": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.indexOf; },
/* harmony export */   "inherits": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.inherits; },
/* harmony export */   "reduce": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.reduce; },
/* harmony export */   "filter": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.filter; },
/* harmony export */   "bind": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.bind; },
/* harmony export */   "curry": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.curry; },
/* harmony export */   "isArray": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.isArray; },
/* harmony export */   "isString": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.isString; },
/* harmony export */   "isObject": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.isObject; },
/* harmony export */   "isFunction": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.isFunction; },
/* harmony export */   "extend": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.extend; },
/* harmony export */   "defaults": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.defaults; },
/* harmony export */   "clone": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.clone; },
/* harmony export */   "merge": function() { return /* reexport safe */ zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__.merge; }
/* harmony export */ });
/* harmony import */ var zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zrender/lib/core/util.js */ "./node_modules/zrender/lib/core/util.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/***/ }),

/***/ "./node_modules/echarts/lib/export/core.js":
/*!*************************************************!*\
  !*** ./node_modules/echarts/lib/export/core.js ***!
  \*************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "PRIORITY": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.PRIORITY; },
/* harmony export */   "connect": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.connect; },
/* harmony export */   "dataTool": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.dataTool; },
/* harmony export */   "dependencies": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.dependencies; },
/* harmony export */   "disConnect": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.disConnect; },
/* harmony export */   "disconnect": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.disconnect; },
/* harmony export */   "dispose": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.dispose; },
/* harmony export */   "getCoordinateSystemDimensions": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.getCoordinateSystemDimensions; },
/* harmony export */   "getInstanceByDom": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.getInstanceByDom; },
/* harmony export */   "getInstanceById": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.getInstanceById; },
/* harmony export */   "getMap": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.getMap; },
/* harmony export */   "init": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.init; },
/* harmony export */   "registerAction": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerAction; },
/* harmony export */   "registerCoordinateSystem": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerCoordinateSystem; },
/* harmony export */   "registerLayout": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerLayout; },
/* harmony export */   "registerLoading": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerLoading; },
/* harmony export */   "registerLocale": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerLocale; },
/* harmony export */   "registerMap": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerMap; },
/* harmony export */   "registerPostInit": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerPostInit; },
/* harmony export */   "registerPostUpdate": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerPostUpdate; },
/* harmony export */   "registerPreprocessor": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerPreprocessor; },
/* harmony export */   "registerProcessor": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerProcessor; },
/* harmony export */   "registerTheme": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerTheme; },
/* harmony export */   "registerTransform": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerTransform; },
/* harmony export */   "registerUpdateLifecycle": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerUpdateLifecycle; },
/* harmony export */   "registerVisual": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.registerVisual; },
/* harmony export */   "setCanvasCreator": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.setCanvasCreator; },
/* harmony export */   "version": function() { return /* reexport safe */ _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__.version; },
/* harmony export */   "Axis": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.Axis; },
/* harmony export */   "ChartView": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.ChartView; },
/* harmony export */   "ComponentModel": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.ComponentModel; },
/* harmony export */   "ComponentView": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.ComponentView; },
/* harmony export */   "List": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.List; },
/* harmony export */   "Model": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.Model; },
/* harmony export */   "SeriesModel": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.SeriesModel; },
/* harmony export */   "color": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.color; },
/* harmony export */   "env": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.env; },
/* harmony export */   "extendChartView": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.extendChartView; },
/* harmony export */   "extendComponentModel": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.extendComponentModel; },
/* harmony export */   "extendComponentView": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.extendComponentView; },
/* harmony export */   "extendSeriesModel": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.extendSeriesModel; },
/* harmony export */   "format": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.format; },
/* harmony export */   "graphic": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.graphic; },
/* harmony export */   "helper": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.helper; },
/* harmony export */   "innerDrawElementOnCanvas": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.innerDrawElementOnCanvas; },
/* harmony export */   "matrix": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.matrix; },
/* harmony export */   "number": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.number; },
/* harmony export */   "parseGeoJSON": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.parseGeoJSON; },
/* harmony export */   "parseGeoJson": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.parseGeoJson; },
/* harmony export */   "setPlatformAPI": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.setPlatformAPI; },
/* harmony export */   "throttle": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.throttle; },
/* harmony export */   "time": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.time; },
/* harmony export */   "use": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.use; },
/* harmony export */   "util": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.util; },
/* harmony export */   "vector": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.vector; },
/* harmony export */   "zrUtil": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.zrUtil; },
/* harmony export */   "zrender": function() { return /* reexport safe */ _api_js__WEBPACK_IMPORTED_MODULE_1__.zrender; }
/* harmony export */ });
/* harmony import */ var _core_echarts_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/echarts.js */ "./node_modules/echarts/lib/core/echarts.js");
/* harmony import */ var _api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api.js */ "./node_modules/echarts/lib/export/api.js");
/* harmony import */ var _extension_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../extension.js */ "./node_modules/echarts/lib/extension.js");
/* harmony import */ var _label_installLabelLayout_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../label/installLabelLayout.js */ "./node_modules/echarts/lib/label/installLabelLayout.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/
// Core API from echarts/src/echarts



// Import label layout by default.
// TODO will be treeshaked.

(0,_extension_js__WEBPACK_IMPORTED_MODULE_2__.use)(_label_installLabelLayout_js__WEBPACK_IMPORTED_MODULE_3__.installLabelLayout);

/***/ }),

/***/ "./node_modules/echarts/lib/label/LabelManager.js":
/*!********************************************************!*\
  !*** ./node_modules/echarts/lib/label/LabelManager.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/graphic.js */ "./node_modules/zrender/lib/core/BoundingRect.js");
/* harmony import */ var _util_graphic_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../util/graphic.js */ "./node_modules/echarts/lib/animation/basicTransition.js");
/* harmony import */ var _util_innerStore_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/innerStore.js */ "./node_modules/echarts/lib/util/innerStore.js");
/* harmony import */ var _util_number_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../util/number.js */ "./node_modules/echarts/lib/util/number.js");
/* harmony import */ var zrender_lib_core_Transformable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zrender/lib/core/Transformable.js */ "./node_modules/zrender/lib/core/Transformable.js");
/* harmony import */ var _labelGuideHelper_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./labelGuideHelper.js */ "./node_modules/echarts/lib/label/labelGuideHelper.js");
/* harmony import */ var _util_model_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/model.js */ "./node_modules/echarts/lib/util/model.js");
/* harmony import */ var zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zrender/lib/core/util.js */ "./node_modules/zrender/lib/core/util.js");
/* harmony import */ var _labelLayoutHelper_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./labelLayoutHelper.js */ "./node_modules/echarts/lib/label/labelLayoutHelper.js");
/* harmony import */ var _labelStyle_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./labelStyle.js */ "./node_modules/echarts/lib/label/labelStyle.js");
/* harmony import */ var zrender_lib_contain_util_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zrender/lib/contain/util.js */ "./node_modules/zrender/lib/contain/util.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/
// TODO: move labels out of viewport.










function cloneArr(points) {
  if (points) {
    var newPoints = [];
    for (var i = 0; i < points.length; i++) {
      newPoints.push(points[i].slice());
    }
    return newPoints;
  }
}
function prepareLayoutCallbackParams(labelItem, hostEl) {
  var label = labelItem.label;
  var labelLine = hostEl && hostEl.getTextGuideLine();
  return {
    dataIndex: labelItem.dataIndex,
    dataType: labelItem.dataType,
    seriesIndex: labelItem.seriesModel.seriesIndex,
    text: labelItem.label.style.text,
    rect: labelItem.hostRect,
    labelRect: labelItem.rect,
    // x: labelAttr.x,
    // y: labelAttr.y,
    align: label.style.align,
    verticalAlign: label.style.verticalAlign,
    labelLinePoints: cloneArr(labelLine && labelLine.shape.points)
  };
}
var LABEL_OPTION_TO_STYLE_KEYS = ['align', 'verticalAlign', 'width', 'height', 'fontSize'];
var dummyTransformable = new zrender_lib_core_Transformable_js__WEBPACK_IMPORTED_MODULE_0__.default();
var labelLayoutInnerStore = (0,_util_model_js__WEBPACK_IMPORTED_MODULE_1__.makeInner)();
var labelLineAnimationStore = (0,_util_model_js__WEBPACK_IMPORTED_MODULE_1__.makeInner)();
function extendWithKeys(target, source, keys) {
  for (var i = 0; i < keys.length; i++) {
    var key = keys[i];
    if (source[key] != null) {
      target[key] = source[key];
    }
  }
}
var LABEL_LAYOUT_PROPS = ['x', 'y', 'rotation'];
var LabelManager = /** @class */function () {
  function LabelManager() {
    this._labelList = [];
    this._chartViewList = [];
  }
  LabelManager.prototype.clearLabels = function () {
    this._labelList = [];
    this._chartViewList = [];
  };
  /**
   * Add label to manager
   */
  LabelManager.prototype._addLabel = function (dataIndex, dataType, seriesModel, label, layoutOption) {
    var labelStyle = label.style;
    var hostEl = label.__hostTarget;
    var textConfig = hostEl.textConfig || {};
    // TODO: If label is in other state.
    var labelTransform = label.getComputedTransform();
    var labelRect = label.getBoundingRect().plain();
    _util_graphic_js__WEBPACK_IMPORTED_MODULE_2__.default.applyTransform(labelRect, labelRect, labelTransform);
    if (labelTransform) {
      dummyTransformable.setLocalTransform(labelTransform);
    } else {
      // Identity transform.
      dummyTransformable.x = dummyTransformable.y = dummyTransformable.rotation = dummyTransformable.originX = dummyTransformable.originY = 0;
      dummyTransformable.scaleX = dummyTransformable.scaleY = 1;
    }
    dummyTransformable.rotation = (0,zrender_lib_contain_util_js__WEBPACK_IMPORTED_MODULE_3__.normalizeRadian)(dummyTransformable.rotation);
    var host = label.__hostTarget;
    var hostRect;
    if (host) {
      hostRect = host.getBoundingRect().plain();
      var transform = host.getComputedTransform();
      _util_graphic_js__WEBPACK_IMPORTED_MODULE_2__.default.applyTransform(hostRect, hostRect, transform);
    }
    var labelGuide = hostRect && host.getTextGuideLine();
    this._labelList.push({
      label: label,
      labelLine: labelGuide,
      seriesModel: seriesModel,
      dataIndex: dataIndex,
      dataType: dataType,
      layoutOption: layoutOption,
      computedLayoutOption: null,
      rect: labelRect,
      hostRect: hostRect,
      // Label with lower priority will be hidden when overlapped
      // Use rect size as default priority
      priority: hostRect ? hostRect.width * hostRect.height : 0,
      // Save default label attributes.
      // For restore if developers want get back to default value in callback.
      defaultAttr: {
        ignore: label.ignore,
        labelGuideIgnore: labelGuide && labelGuide.ignore,
        x: dummyTransformable.x,
        y: dummyTransformable.y,
        scaleX: dummyTransformable.scaleX,
        scaleY: dummyTransformable.scaleY,
        rotation: dummyTransformable.rotation,
        style: {
          x: labelStyle.x,
          y: labelStyle.y,
          align: labelStyle.align,
          verticalAlign: labelStyle.verticalAlign,
          width: labelStyle.width,
          height: labelStyle.height,
          fontSize: labelStyle.fontSize
        },
        cursor: label.cursor,
        attachedPos: textConfig.position,
        attachedRot: textConfig.rotation
      }
    });
  };
  LabelManager.prototype.addLabelsOfSeries = function (chartView) {
    var _this = this;
    this._chartViewList.push(chartView);
    var seriesModel = chartView.__model;
    var layoutOption = seriesModel.get('labelLayout');
    /**
     * Ignore layouting if it's not specified anything.
     */
    if (!((0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.isFunction)(layoutOption) || (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.keys)(layoutOption).length)) {
      return;
    }
    chartView.group.traverse(function (child) {
      if (child.ignore) {
        return true; // Stop traverse descendants.
      }
      // Only support label being hosted on graphic elements.
      var textEl = child.getTextContent();
      var ecData = (0,_util_innerStore_js__WEBPACK_IMPORTED_MODULE_5__.getECData)(child);
      // Can only attach the text on the element with dataIndex
      if (textEl && !textEl.disableLabelLayout) {
        _this._addLabel(ecData.dataIndex, ecData.dataType, seriesModel, textEl, layoutOption);
      }
    });
  };
  LabelManager.prototype.updateLayoutConfig = function (api) {
    var width = api.getWidth();
    var height = api.getHeight();
    function createDragHandler(el, labelLineModel) {
      return function () {
        (0,_labelGuideHelper_js__WEBPACK_IMPORTED_MODULE_6__.updateLabelLinePoints)(el, labelLineModel);
      };
    }
    for (var i = 0; i < this._labelList.length; i++) {
      var labelItem = this._labelList[i];
      var label = labelItem.label;
      var hostEl = label.__hostTarget;
      var defaultLabelAttr = labelItem.defaultAttr;
      var layoutOption = void 0;
      // TODO A global layout option?
      if ((0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.isFunction)(labelItem.layoutOption)) {
        layoutOption = labelItem.layoutOption(prepareLayoutCallbackParams(labelItem, hostEl));
      } else {
        layoutOption = labelItem.layoutOption;
      }
      layoutOption = layoutOption || {};
      labelItem.computedLayoutOption = layoutOption;
      var degreeToRadian = Math.PI / 180;
      // TODO hostEl should always exists.
      // Or label should not have parent because the x, y is all in global space.
      if (hostEl) {
        hostEl.setTextConfig({
          // Force to set local false.
          local: false,
          // Ignore position and rotation config on the host el if x or y is changed.
          position: layoutOption.x != null || layoutOption.y != null ? null : defaultLabelAttr.attachedPos,
          // Ignore rotation config on the host el if rotation is changed.
          rotation: layoutOption.rotate != null ? layoutOption.rotate * degreeToRadian : defaultLabelAttr.attachedRot,
          offset: [layoutOption.dx || 0, layoutOption.dy || 0]
        });
      }
      var needsUpdateLabelLine = false;
      if (layoutOption.x != null) {
        // TODO width of chart view.
        label.x = (0,_util_number_js__WEBPACK_IMPORTED_MODULE_7__.parsePercent)(layoutOption.x, width);
        label.setStyle('x', 0); // Ignore movement in style. TODO: origin.
        needsUpdateLabelLine = true;
      } else {
        label.x = defaultLabelAttr.x;
        label.setStyle('x', defaultLabelAttr.style.x);
      }
      if (layoutOption.y != null) {
        // TODO height of chart view.
        label.y = (0,_util_number_js__WEBPACK_IMPORTED_MODULE_7__.parsePercent)(layoutOption.y, height);
        label.setStyle('y', 0); // Ignore movement in style.
        needsUpdateLabelLine = true;
      } else {
        label.y = defaultLabelAttr.y;
        label.setStyle('y', defaultLabelAttr.style.y);
      }
      if (layoutOption.labelLinePoints) {
        var guideLine = hostEl.getTextGuideLine();
        if (guideLine) {
          guideLine.setShape({
            points: layoutOption.labelLinePoints
          });
          // Not update
          needsUpdateLabelLine = false;
        }
      }
      var labelLayoutStore = labelLayoutInnerStore(label);
      labelLayoutStore.needsUpdateLabelLine = needsUpdateLabelLine;
      label.rotation = layoutOption.rotate != null ? layoutOption.rotate * degreeToRadian : defaultLabelAttr.rotation;
      label.scaleX = defaultLabelAttr.scaleX;
      label.scaleY = defaultLabelAttr.scaleY;
      for (var k = 0; k < LABEL_OPTION_TO_STYLE_KEYS.length; k++) {
        var key = LABEL_OPTION_TO_STYLE_KEYS[k];
        label.setStyle(key, layoutOption[key] != null ? layoutOption[key] : defaultLabelAttr.style[key]);
      }
      if (layoutOption.draggable) {
        label.draggable = true;
        label.cursor = 'move';
        if (hostEl) {
          var hostModel = labelItem.seriesModel;
          if (labelItem.dataIndex != null) {
            var data = labelItem.seriesModel.getData(labelItem.dataType);
            hostModel = data.getItemModel(labelItem.dataIndex);
          }
          label.on('drag', createDragHandler(hostEl, hostModel.getModel('labelLine')));
        }
      } else {
        // TODO Other drag functions?
        label.off('drag');
        label.cursor = defaultLabelAttr.cursor;
      }
    }
  };
  LabelManager.prototype.layout = function (api) {
    var width = api.getWidth();
    var height = api.getHeight();
    var labelList = (0,_labelLayoutHelper_js__WEBPACK_IMPORTED_MODULE_8__.prepareLayoutList)(this._labelList);
    var labelsNeedsAdjustOnX = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.filter)(labelList, function (item) {
      return item.layoutOption.moveOverlap === 'shiftX';
    });
    var labelsNeedsAdjustOnY = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.filter)(labelList, function (item) {
      return item.layoutOption.moveOverlap === 'shiftY';
    });
    (0,_labelLayoutHelper_js__WEBPACK_IMPORTED_MODULE_8__.shiftLayoutOnX)(labelsNeedsAdjustOnX, 0, width);
    (0,_labelLayoutHelper_js__WEBPACK_IMPORTED_MODULE_8__.shiftLayoutOnY)(labelsNeedsAdjustOnY, 0, height);
    var labelsNeedsHideOverlap = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.filter)(labelList, function (item) {
      return item.layoutOption.hideOverlap;
    });
    (0,_labelLayoutHelper_js__WEBPACK_IMPORTED_MODULE_8__.hideOverlap)(labelsNeedsHideOverlap);
  };
  /**
   * Process all labels. Not only labels with layoutOption.
   */
  LabelManager.prototype.processLabelsOverall = function () {
    var _this = this;
    (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.each)(this._chartViewList, function (chartView) {
      var seriesModel = chartView.__model;
      var ignoreLabelLineUpdate = chartView.ignoreLabelLineUpdate;
      var animationEnabled = seriesModel.isAnimationEnabled();
      chartView.group.traverse(function (child) {
        if (child.ignore && !child.forceLabelAnimation) {
          return true; // Stop traverse descendants.
        }
        var needsUpdateLabelLine = !ignoreLabelLineUpdate;
        var label = child.getTextContent();
        if (!needsUpdateLabelLine && label) {
          needsUpdateLabelLine = labelLayoutInnerStore(label).needsUpdateLabelLine;
        }
        if (needsUpdateLabelLine) {
          _this._updateLabelLine(child, seriesModel);
        }
        if (animationEnabled) {
          _this._animateLabels(child, seriesModel);
        }
      });
    });
  };
  LabelManager.prototype._updateLabelLine = function (el, seriesModel) {
    // Only support label being hosted on graphic elements.
    var textEl = el.getTextContent();
    // Update label line style.
    var ecData = (0,_util_innerStore_js__WEBPACK_IMPORTED_MODULE_5__.getECData)(el);
    var dataIndex = ecData.dataIndex;
    // Only support labelLine on the labels represent data.
    if (textEl && dataIndex != null) {
      var data = seriesModel.getData(ecData.dataType);
      var itemModel = data.getItemModel(dataIndex);
      var defaultStyle = {};
      var visualStyle = data.getItemVisual(dataIndex, 'style');
      if (visualStyle) {
        var visualType = data.getVisual('drawType');
        // Default to be same with main color
        defaultStyle.stroke = visualStyle[visualType];
      }
      var labelLineModel = itemModel.getModel('labelLine');
      (0,_labelGuideHelper_js__WEBPACK_IMPORTED_MODULE_6__.setLabelLineStyle)(el, (0,_labelGuideHelper_js__WEBPACK_IMPORTED_MODULE_6__.getLabelLineStatesModels)(itemModel), defaultStyle);
      (0,_labelGuideHelper_js__WEBPACK_IMPORTED_MODULE_6__.updateLabelLinePoints)(el, labelLineModel);
    }
  };
  LabelManager.prototype._animateLabels = function (el, seriesModel) {
    var textEl = el.getTextContent();
    var guideLine = el.getTextGuideLine();
    // Animate
    if (textEl
    // `forceLabelAnimation` has the highest priority
    && (el.forceLabelAnimation || !textEl.ignore && !textEl.invisible && !el.disableLabelAnimation && !(0,_util_graphic_js__WEBPACK_IMPORTED_MODULE_9__.isElementRemoved)(el))) {
      var layoutStore = labelLayoutInnerStore(textEl);
      var oldLayout = layoutStore.oldLayout;
      var ecData = (0,_util_innerStore_js__WEBPACK_IMPORTED_MODULE_5__.getECData)(el);
      var dataIndex = ecData.dataIndex;
      var newProps = {
        x: textEl.x,
        y: textEl.y,
        rotation: textEl.rotation
      };
      var data = seriesModel.getData(ecData.dataType);
      if (!oldLayout) {
        textEl.attr(newProps);
        // Disable fade in animation if value animation is enabled.
        if (!(0,_labelStyle_js__WEBPACK_IMPORTED_MODULE_10__.labelInner)(textEl).valueAnimation) {
          var oldOpacity = (0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.retrieve2)(textEl.style.opacity, 1);
          // Fade in animation
          textEl.style.opacity = 0;
          (0,_util_graphic_js__WEBPACK_IMPORTED_MODULE_9__.initProps)(textEl, {
            style: {
              opacity: oldOpacity
            }
          }, seriesModel, dataIndex);
        }
      } else {
        textEl.attr(oldLayout);
        // Make sure the animation from is in the right status.
        var prevStates = el.prevStates;
        if (prevStates) {
          if ((0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.indexOf)(prevStates, 'select') >= 0) {
            textEl.attr(layoutStore.oldLayoutSelect);
          }
          if ((0,zrender_lib_core_util_js__WEBPACK_IMPORTED_MODULE_4__.indexOf)(prevStates, 'emphasis') >= 0) {
            textEl.attr(layoutStore.oldLayoutEmphasis);
          }
        }
        (0,_util_graphic_js__WEBPACK_IMPORTED_MODULE_9__.updateProps)(textEl, newProps, seriesModel, dataIndex);
      }
      layoutStore.oldLayout = newProps;
      if (textEl.states.select) {
        var layoutSelect = layoutStore.oldLayoutSelect = {};
        extendWithKeys(layoutSelect, newProps, LABEL_LAYOUT_PROPS);
        extendWithKeys(layoutSelect, textEl.states.select, LABEL_LAYOUT_PROPS);
      }
      if (textEl.states.emphasis) {
        var layoutEmphasis = layoutStore.oldLayoutEmphasis = {};
        extendWithKeys(layoutEmphasis, newProps, LABEL_LAYOUT_PROPS);
        extendWithKeys(layoutEmphasis, textEl.states.emphasis, LABEL_LAYOUT_PROPS);
      }
      (0,_labelStyle_js__WEBPACK_IMPORTED_MODULE_10__.animateLabelValue)(textEl, dataIndex, data, seriesModel, seriesModel);
    }
    if (guideLine && !guideLine.ignore && !guideLine.invisible) {
      var layoutStore = labelLineAnimationStore(guideLine);
      var oldLayout = layoutStore.oldLayout;
      var newLayout = {
        points: guideLine.shape.points
      };
      if (!oldLayout) {
        guideLine.setShape(newLayout);
        guideLine.style.strokePercent = 0;
        (0,_util_graphic_js__WEBPACK_IMPORTED_MODULE_9__.initProps)(guideLine, {
          style: {
            strokePercent: 1
          }
        }, seriesModel);
      } else {
        guideLine.attr({
          shape: oldLayout
        });
        (0,_util_graphic_js__WEBPACK_IMPORTED_MODULE_9__.updateProps)(guideLine, {
          shape: newLayout
        }, seriesModel);
      }
      layoutStore.oldLayout = newLayout;
    }
  };
  return LabelManager;
}();
/* harmony default export */ __webpack_exports__["default"] = (LabelManager);

/***/ }),

/***/ "./node_modules/echarts/lib/label/installLabelLayout.js":
/*!**************************************************************!*\
  !*** ./node_modules/echarts/lib/label/installLabelLayout.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "installLabelLayout": function() { return /* binding */ installLabelLayout; }
/* harmony export */ });
/* harmony import */ var _util_model_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/model.js */ "./node_modules/echarts/lib/util/model.js");
/* harmony import */ var _LabelManager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./LabelManager.js */ "./node_modules/echarts/lib/label/LabelManager.js");

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


var getLabelManager = (0,_util_model_js__WEBPACK_IMPORTED_MODULE_0__.makeInner)();
function installLabelLayout(registers) {
  registers.registerUpdateLifecycle('series:beforeupdate', function (ecModel, api, params) {
    // TODO api provide an namespace that can save stuff per instance
    var labelManager = getLabelManager(api).labelManager;
    if (!labelManager) {
      labelManager = getLabelManager(api).labelManager = new _LabelManager_js__WEBPACK_IMPORTED_MODULE_1__.default();
    }
    labelManager.clearLabels();
  });
  registers.registerUpdateLifecycle('series:layoutlabels', function (ecModel, api, params) {
    var labelManager = getLabelManager(api).labelManager;
    params.updatedSeries.forEach(function (series) {
      labelManager.addLabelsOfSeries(api.getViewOfSeriesModel(series));
    });
    labelManager.updateLayoutConfig(api);
    labelManager.layout(api);
    labelManager.processLabelsOverall();
  });
}

/***/ })

}]);