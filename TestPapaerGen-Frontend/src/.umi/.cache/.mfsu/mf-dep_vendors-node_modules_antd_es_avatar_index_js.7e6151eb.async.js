(self["webpackChunk"] = self["webpackChunk"] || []).push([["mf-dep_vendors-node_modules_antd_es_avatar_index_js"],{

/***/ "./node_modules/antd/es/_util/getRenderPropValue.js":
/*!**********************************************************!*\
  !*** ./node_modules/antd/es/_util/getRenderPropValue.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "getRenderPropValue": function() { return /* binding */ getRenderPropValue; }
/* harmony export */ });
var getRenderPropValue = function getRenderPropValue(propValue) {
  if (!propValue) {
    return null;
  }

  if (typeof propValue === 'function') {
    return propValue();
  }

  return propValue;
};

/***/ }),

/***/ "./node_modules/antd/es/_util/hooks/useForceUpdate.js":
/*!************************************************************!*\
  !*** ./node_modules/antd/es/_util/hooks/useForceUpdate.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useForceUpdate; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


function useForceUpdate() {
  var _React$useReducer = react__WEBPACK_IMPORTED_MODULE_1__.useReducer(function (x) {
    return x + 1;
  }, 0),
      _React$useReducer2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__.default)(_React$useReducer, 2),
      forceUpdate = _React$useReducer2[1];

  return forceUpdate;
}

/***/ }),

/***/ "./node_modules/antd/es/_util/responsiveObserve.js":
/*!*********************************************************!*\
  !*** ./node_modules/antd/es/_util/responsiveObserve.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "responsiveArray": function() { return /* binding */ responsiveArray; },
/* harmony export */   "responsiveMap": function() { return /* binding */ responsiveMap; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");


var responsiveArray = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];
var responsiveMap = {
  xs: '(max-width: 575px)',
  sm: '(min-width: 576px)',
  md: '(min-width: 768px)',
  lg: '(min-width: 992px)',
  xl: '(min-width: 1200px)',
  xxl: '(min-width: 1600px)'
};
var subscribers = new Map();
var subUid = -1;
var screens = {};
var responsiveObserve = {
  matchHandlers: {},
  dispatch: function dispatch(pointMap) {
    screens = pointMap;
    subscribers.forEach(function (func) {
      return func(screens);
    });
    return subscribers.size >= 1;
  },
  subscribe: function subscribe(func) {
    if (!subscribers.size) this.register();
    subUid += 1;
    subscribers.set(subUid, func);
    func(screens);
    return subUid;
  },
  unsubscribe: function unsubscribe(token) {
    subscribers["delete"](token);
    if (!subscribers.size) this.unregister();
  },
  unregister: function unregister() {
    var _this = this;

    Object.keys(responsiveMap).forEach(function (screen) {
      var matchMediaQuery = responsiveMap[screen];
      var handler = _this.matchHandlers[matchMediaQuery];
      handler === null || handler === void 0 ? void 0 : handler.mql.removeListener(handler === null || handler === void 0 ? void 0 : handler.listener);
    });
    subscribers.clear();
  },
  register: function register() {
    var _this2 = this;

    Object.keys(responsiveMap).forEach(function (screen) {
      var matchMediaQuery = responsiveMap[screen];

      var listener = function listener(_ref) {
        var matches = _ref.matches;

        _this2.dispatch((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, screens), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)({}, screen, matches)));
      };

      var mql = window.matchMedia(matchMediaQuery);
      mql.addListener(listener);
      _this2.matchHandlers[matchMediaQuery] = {
        mql: mql,
        listener: listener
      };
      listener(mql);
    });
  }
};
/* harmony default export */ __webpack_exports__["default"] = (responsiveObserve);

/***/ }),

/***/ "./node_modules/antd/es/avatar/SizeContext.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/avatar/SizeContext.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "SizeContextProvider": function() { return /* binding */ SizeContextProvider; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

var SizeContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext('default');
var SizeContextProvider = function SizeContextProvider(_ref) {
  var children = _ref.children,
      size = _ref.size;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(SizeContext.Consumer, null, function (originSize) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(SizeContext.Provider, {
      value: size || originSize
    }, children);
  });
};
/* harmony default export */ __webpack_exports__["default"] = (SizeContext);

/***/ }),

/***/ "./node_modules/antd/es/avatar/avatar.js":
/*!***********************************************!*\
  !*** ./node_modules/antd/es/avatar/avatar.js ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-resize-observer */ "./node_modules/rc-resize-observer/es/index.js");
/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/ref */ "./node_modules/rc-util/es/ref.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _grid_hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../grid/hooks/useBreakpoint */ "./node_modules/antd/es/grid/hooks/useBreakpoint.js");
/* harmony import */ var _util_responsiveObserve__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../_util/responsiveObserve */ "./node_modules/antd/es/_util/responsiveObserve.js");
/* harmony import */ var _util_warning__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../_util/warning */ "./node_modules/antd/es/_util/warning.js");
/* harmony import */ var _SizeContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SizeContext */ "./node_modules/antd/es/avatar/SizeContext.js");





var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};











var InternalAvatar = function InternalAvatar(props, ref) {
  var groupSize = react__WEBPACK_IMPORTED_MODULE_7__.useContext(_SizeContext__WEBPACK_IMPORTED_MODULE_8__.default);

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_7__.useState(1),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__.default)(_React$useState, 2),
      scale = _React$useState2[0],
      setScale = _React$useState2[1];

  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_7__.useState(false),
      _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__.default)(_React$useState3, 2),
      mounted = _React$useState4[0],
      setMounted = _React$useState4[1];

  var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_7__.useState(true),
      _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__.default)(_React$useState5, 2),
      isImgExist = _React$useState6[0],
      setIsImgExist = _React$useState6[1];

  var avatarNodeRef = react__WEBPACK_IMPORTED_MODULE_7__.useRef(null);
  var avatarChildrenRef = react__WEBPACK_IMPORTED_MODULE_7__.useRef(null);
  var avatarNodeMergeRef = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.composeRef)(ref, avatarNodeRef);

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_7__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_9__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls;

  var setScaleParam = function setScaleParam() {
    if (!avatarChildrenRef.current || !avatarNodeRef.current) {
      return;
    }

    var childrenWidth = avatarChildrenRef.current.offsetWidth; // offsetWidth avoid affecting be transform scale

    var nodeWidth = avatarNodeRef.current.offsetWidth; // denominator is 0 is no meaning

    if (childrenWidth !== 0 && nodeWidth !== 0) {
      var _props$gap = props.gap,
          gap = _props$gap === void 0 ? 4 : _props$gap;

      if (gap * 2 < nodeWidth) {
        setScale(nodeWidth - gap * 2 < childrenWidth ? (nodeWidth - gap * 2) / childrenWidth : 1);
      }
    }
  };

  react__WEBPACK_IMPORTED_MODULE_7__.useEffect(function () {
    setMounted(true);
  }, []);
  react__WEBPACK_IMPORTED_MODULE_7__.useEffect(function () {
    setIsImgExist(true);
    setScale(1);
  }, [props.src]);
  react__WEBPACK_IMPORTED_MODULE_7__.useEffect(function () {
    setScaleParam();
  }, [props.gap]);

  var handleImgLoadError = function handleImgLoadError() {
    var onError = props.onError;
    var errorFlag = onError ? onError() : undefined;

    if (errorFlag !== false) {
      setIsImgExist(false);
    }
  };

  var customizePrefixCls = props.prefixCls,
      _props$shape = props.shape,
      shape = _props$shape === void 0 ? 'circle' : _props$shape,
      _props$size = props.size,
      customSize = _props$size === void 0 ? 'default' : _props$size,
      src = props.src,
      srcSet = props.srcSet,
      icon = props.icon,
      className = props.className,
      alt = props.alt,
      draggable = props.draggable,
      children = props.children,
      crossOrigin = props.crossOrigin,
      others = __rest(props, ["prefixCls", "shape", "size", "src", "srcSet", "icon", "className", "alt", "draggable", "children", "crossOrigin"]);

  var size = customSize === 'default' ? groupSize : customSize;
  var needResponsive = Object.keys((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__.default)(size) === 'object' ? size || {} : {}).some(function (key) {
    return ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'].includes(key);
  });
  var screens = (0,_grid_hooks_useBreakpoint__WEBPACK_IMPORTED_MODULE_10__.default)(needResponsive);
  var responsiveSizeStyle = react__WEBPACK_IMPORTED_MODULE_7__.useMemo(function () {
    if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__.default)(size) !== 'object') {
      return {};
    }

    var currentBreakpoint = _util_responsiveObserve__WEBPACK_IMPORTED_MODULE_11__.responsiveArray.find(function (screen) {
      return screens[screen];
    });
    var currentSize = size[currentBreakpoint];
    return currentSize ? {
      width: currentSize,
      height: currentSize,
      lineHeight: "".concat(currentSize, "px"),
      fontSize: icon ? currentSize / 2 : 18
    } : {};
  }, [screens, size]);
   true ? (0,_util_warning__WEBPACK_IMPORTED_MODULE_12__.default)(!(typeof icon === 'string' && icon.length > 2), 'Avatar', "`icon` is using ReactNode instead of string naming in v4. Please check `".concat(icon, "` at https://ant.design/components/icon")) : 0;
  var prefixCls = getPrefixCls('avatar', customizePrefixCls);
  var sizeCls = classnames__WEBPACK_IMPORTED_MODULE_4___default()((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)({}, "".concat(prefixCls, "-lg"), size === 'large'), "".concat(prefixCls, "-sm"), size === 'small'));
  var hasImageElement = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.isValidElement(src);
  var classString = classnames__WEBPACK_IMPORTED_MODULE_4___default()(prefixCls, sizeCls, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)({}, "".concat(prefixCls, "-").concat(shape), !!shape), "".concat(prefixCls, "-image"), hasImageElement || src && isImgExist), "".concat(prefixCls, "-icon"), !!icon), className);
  var sizeStyle = typeof size === 'number' ? {
    width: size,
    height: size,
    lineHeight: "".concat(size, "px"),
    fontSize: icon ? size / 2 : 18
  } : {};
  var childrenToRender;

  if (typeof src === 'string' && isImgExist) {
    childrenToRender = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("img", {
      src: src,
      draggable: draggable,
      srcSet: srcSet,
      onError: handleImgLoadError,
      alt: alt,
      crossOrigin: crossOrigin
    });
  } else if (hasImageElement) {
    childrenToRender = src;
  } else if (icon) {
    childrenToRender = icon;
  } else if (mounted || scale !== 1) {
    var transformString = "scale(".concat(scale, ") translateX(-50%)");
    var childrenStyle = {
      msTransform: transformString,
      WebkitTransform: transformString,
      transform: transformString
    };
    var sizeChildrenStyle = typeof size === 'number' ? {
      lineHeight: "".concat(size, "px")
    } : {};
    childrenToRender = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__.default, {
      onResize: setScaleParam
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", {
      className: "".concat(prefixCls, "-string"),
      ref: avatarChildrenRef,
      style: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, sizeChildrenStyle), childrenStyle)
    }, children));
  } else {
    childrenToRender = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", {
      className: "".concat(prefixCls, "-string"),
      style: {
        opacity: 0
      },
      ref: avatarChildrenRef
    }, children);
  } // The event is triggered twice from bubbling up the DOM tree.
  // see https://codesandbox.io/s/kind-snow-9lidz


  delete others.onError;
  delete others.gap;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, others, {
    style: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, sizeStyle), responsiveSizeStyle), others.style),
    className: classString,
    ref: avatarNodeMergeRef
  }), childrenToRender);
};

var Avatar = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.forwardRef(InternalAvatar);

if (true) {
  Avatar.displayName = 'Avatar';
}

/* harmony default export */ __webpack_exports__["default"] = (Avatar);

/***/ }),

/***/ "./node_modules/antd/es/avatar/group.js":
/*!**********************************************!*\
  !*** ./node_modules/antd/es/avatar/group.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/Children/toArray */ "./node_modules/rc-util/es/Children/toArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _popover__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../popover */ "./node_modules/antd/es/popover/index.js");
/* harmony import */ var _util_reactNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../_util/reactNode */ "./node_modules/antd/es/_util/reactNode.js");
/* harmony import */ var _avatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./avatar */ "./node_modules/antd/es/avatar/avatar.js");
/* harmony import */ var _SizeContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SizeContext */ "./node_modules/antd/es/avatar/SizeContext.js");










var Group = function Group(props) {
  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_3__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_4__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls,
      direction = _React$useContext.direction;

  var customizePrefixCls = props.prefixCls,
      _props$className = props.className,
      className = _props$className === void 0 ? '' : _props$className,
      maxCount = props.maxCount,
      maxStyle = props.maxStyle,
      size = props.size;
  var prefixCls = getPrefixCls('avatar-group', customizePrefixCls);
  var cls = classnames__WEBPACK_IMPORTED_MODULE_1___default()(prefixCls, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)({}, "".concat(prefixCls, "-rtl"), direction === 'rtl'), className);
  var children = props.children,
      _props$maxPopoverPlac = props.maxPopoverPlacement,
      maxPopoverPlacement = _props$maxPopoverPlac === void 0 ? 'top' : _props$maxPopoverPlac,
      _props$maxPopoverTrig = props.maxPopoverTrigger,
      maxPopoverTrigger = _props$maxPopoverTrig === void 0 ? 'hover' : _props$maxPopoverTrig;
  var childrenWithProps = (0,rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_2__.default)(children).map(function (child, index) {
    return (0,_util_reactNode__WEBPACK_IMPORTED_MODULE_5__.cloneElement)(child, {
      key: "avatar-key-".concat(index)
    });
  });
  var numOfChildren = childrenWithProps.length;

  if (maxCount && maxCount < numOfChildren) {
    var childrenShow = childrenWithProps.slice(0, maxCount);
    var childrenHidden = childrenWithProps.slice(maxCount, numOfChildren);
    childrenShow.push( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_popover__WEBPACK_IMPORTED_MODULE_6__.default, {
      key: "avatar-popover-key",
      content: childrenHidden,
      trigger: maxPopoverTrigger,
      placement: maxPopoverPlacement,
      overlayClassName: "".concat(prefixCls, "-popover")
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_avatar__WEBPACK_IMPORTED_MODULE_7__.default, {
      style: maxStyle
    }, "+".concat(numOfChildren - maxCount))));
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_SizeContext__WEBPACK_IMPORTED_MODULE_8__.SizeContextProvider, {
      size: size
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      className: cls,
      style: props.style
    }, childrenShow));
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_SizeContext__WEBPACK_IMPORTED_MODULE_8__.SizeContextProvider, {
    size: size
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: cls,
    style: props.style
  }, childrenWithProps));
};

/* harmony default export */ __webpack_exports__["default"] = (Group);

/***/ }),

/***/ "./node_modules/antd/es/avatar/index.js":
/*!**********************************************!*\
  !*** ./node_modules/antd/es/avatar/index.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Group": function() { return /* reexport safe */ _group__WEBPACK_IMPORTED_MODULE_0__.default; }
/* harmony export */ });
/* harmony import */ var _avatar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./avatar */ "./node_modules/antd/es/avatar/avatar.js");
/* harmony import */ var _group__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./group */ "./node_modules/antd/es/avatar/group.js");



var Avatar = _avatar__WEBPACK_IMPORTED_MODULE_1__.default;
Avatar.Group = _group__WEBPACK_IMPORTED_MODULE_0__.default;
/* harmony default export */ __webpack_exports__["default"] = (Avatar);

/***/ }),

/***/ "./node_modules/antd/es/grid/hooks/useBreakpoint.js":
/*!**********************************************************!*\
  !*** ./node_modules/antd/es/grid/hooks/useBreakpoint.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _util_hooks_useForceUpdate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_util/hooks/useForceUpdate */ "./node_modules/antd/es/_util/hooks/useForceUpdate.js");
/* harmony import */ var _util_responsiveObserve__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../_util/responsiveObserve */ "./node_modules/antd/es/_util/responsiveObserve.js");




function useBreakpoint() {
  var refreshOnChange = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
  var screensRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});
  var forceUpdate = (0,_util_hooks_useForceUpdate__WEBPACK_IMPORTED_MODULE_1__.default)();
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    var token = _util_responsiveObserve__WEBPACK_IMPORTED_MODULE_2__.default.subscribe(function (supportScreens) {
      screensRef.current = supportScreens;

      if (refreshOnChange) {
        forceUpdate();
      }
    });
    return function () {
      return _util_responsiveObserve__WEBPACK_IMPORTED_MODULE_2__.default.unsubscribe(token);
    };
  }, []);
  return screensRef.current;
}

/* harmony default export */ __webpack_exports__["default"] = (useBreakpoint);

/***/ }),

/***/ "./node_modules/antd/es/popover/index.js":
/*!***********************************************!*\
  !*** ./node_modules/antd/es/popover/index.js ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _util_getRenderPropValue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_util/getRenderPropValue */ "./node_modules/antd/es/_util/getRenderPropValue.js");
/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../_util/motion */ "./node_modules/antd/es/_util/motion.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../tooltip */ "./node_modules/antd/es/tooltip/index.js");


var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};







var Overlay = function Overlay(_ref) {
  var title = _ref.title,
      content = _ref.content,
      prefixCls = _ref.prefixCls;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, title && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: "".concat(prefixCls, "-title")
  }, (0,_util_getRenderPropValue__WEBPACK_IMPORTED_MODULE_2__.getRenderPropValue)(title)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: "".concat(prefixCls, "-inner-content")
  }, (0,_util_getRenderPropValue__WEBPACK_IMPORTED_MODULE_2__.getRenderPropValue)(content)));
};

var Popover = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function (props, ref) {
  var customizePrefixCls = props.prefixCls,
      title = props.title,
      content = props.content,
      _overlay = props._overlay,
      _props$placement = props.placement,
      placement = _props$placement === void 0 ? 'top' : _props$placement,
      _props$trigger = props.trigger,
      trigger = _props$trigger === void 0 ? 'hover' : _props$trigger,
      _props$mouseEnterDela = props.mouseEnterDelay,
      mouseEnterDelay = _props$mouseEnterDela === void 0 ? 0.1 : _props$mouseEnterDela,
      _props$mouseLeaveDela = props.mouseLeaveDelay,
      mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,
      _props$overlayStyle = props.overlayStyle,
      overlayStyle = _props$overlayStyle === void 0 ? {} : _props$overlayStyle,
      otherProps = __rest(props, ["prefixCls", "title", "content", "_overlay", "placement", "trigger", "mouseEnterDelay", "mouseLeaveDelay", "overlayStyle"]);

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_3__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls;

  var prefixCls = getPrefixCls('popover', customizePrefixCls);
  var rootPrefixCls = getPrefixCls();
  var mergedOverlay = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(function () {
    if (_overlay) {
      return _overlay;
    }

    if (!title && !content) {
      return null;
    }

    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Overlay, {
      prefixCls: prefixCls,
      title: title,
      content: content
    });
  }, [_overlay, title, content, prefixCls]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_tooltip__WEBPACK_IMPORTED_MODULE_4__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
    placement: placement,
    trigger: trigger,
    mouseEnterDelay: mouseEnterDelay,
    mouseLeaveDelay: mouseLeaveDelay,
    overlayStyle: overlayStyle
  }, otherProps, {
    prefixCls: prefixCls,
    ref: ref,
    overlay: mergedOverlay,
    transitionName: (0,_util_motion__WEBPACK_IMPORTED_MODULE_5__.getTransitionName)(rootPrefixCls, 'zoom-big', otherProps.transitionName)
  }));
});

if (true) {
  Popover.displayName = 'Popover';
}

/* harmony default export */ __webpack_exports__["default"] = (Popover);

/***/ }),

/***/ "./node_modules/rc-util/es/Children/toArray.js":
/*!*****************************************************!*\
  !*** ./node_modules/rc-util/es/Children/toArray.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ toArray; }
/* harmony export */ });
/* harmony import */ var _React_isFragment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../React/isFragment */ "./node_modules/rc-util/es/React/isFragment.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


function toArray(children) {
  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var ret = [];
  react__WEBPACK_IMPORTED_MODULE_1__.Children.forEach(children, function (child) {
    if ((child === undefined || child === null) && !option.keepEmpty) {
      return;
    }

    if (Array.isArray(child)) {
      ret = ret.concat(toArray(child));
    } else if ((0,_React_isFragment__WEBPACK_IMPORTED_MODULE_0__.default)(child) && child.props) {
      ret = ret.concat(toArray(child.props.children, option));
    } else {
      ret.push(child);
    }
  });
  return ret;
}

/***/ })

}]);