// 主题颜色定义
@primary-color: rgb(245, 210, 128);
@primary-light: rgb(255, 248, 220);
@primary-dark: rgb(139, 117, 61);
@text-color: rgb(51, 51, 51);

.table_wrapper {
  margin: 10px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, @primary-light 0%, #ffffff 100%);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(245, 210, 128, 0.1);
}

.column_small_text {
  font-size: smaller;
  color: @text-color;
}

.tableRow {
  cursor: pointer;
  transition: all 300ms ease;
  border-radius: 8px;
}

.tableRow:hover {
  box-shadow: 0 4px 20px rgba(245, 210, 128, 0.3);
  transform: scale(1.002);
  background: rgba(245, 210, 128, 0.1);
}

.tableRow_selected {
  cursor: pointer;
  color: @primary-dark;
  background: linear-gradient(135deg, @primary-light 0%, rgba(245, 210, 128, 0.3) 100%);
  border: 1px solid @primary-color;
  border-radius: 8px;
}
