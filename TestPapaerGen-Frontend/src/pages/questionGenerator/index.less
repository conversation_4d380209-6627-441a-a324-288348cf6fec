// 主题颜色定义
@primary-color: rgb(245, 210, 128);
@primary-light: rgb(255, 248, 220);
@primary-dark: rgb(139, 117, 61);
@text-color: rgb(51, 51, 51);

// 主容器布局
.flex_wrapper {
  display: grid;
  grid-template-columns: 1fr 400px;
  grid-template-rows: auto auto;
  gap: 24px;
  background: linear-gradient(135deg, @primary-light 0%, #ffffff 100%);
  border-radius: 16px;
  padding: 24px;
  margin: 16px;
  box-shadow: 0 8px 32px rgba(245, 210, 128, 0.1);
  min-height: calc(100vh - 200px);
}

// 左侧题目预览区域
.wrapper_left_side {
  grid-column: 1;
  grid-row: 1 / 3;

  .ant-card {
    border-radius: 16px;
    border: 1px solid rgba(245, 210, 128, 0.3);
    box-shadow: 0 6px 24px rgba(245, 210, 128, 0.12);
    margin-bottom: 16px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 32px rgba(245, 210, 128, 0.18);
      transform: translateY(-2px);
    }

    .ant-card-head {
      background: linear-gradient(135deg, @primary-light 0%, rgba(255, 255, 255, 0.9) 100%);
      border-bottom: 1px solid rgba(245, 210, 128, 0.3);
      border-radius: 16px 16px 0 0;
      padding: 16px 24px;
    }

    .ant-card-body {
      padding: 20px 24px;
      max-height: 600px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(245, 210, 128, 0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(245, 210, 128, 0.4);
        border-radius: 3px;

        &:hover {
          background: rgba(245, 210, 128, 0.6);
        }
      }
    }
  }
}

// 右上：自动组卷功能区
.wrapper_right_side {
  .ant-card {
    border-radius: 16px;
    border: 1px solid rgba(245, 210, 128, 0.3);
    box-shadow: 0 6px 24px rgba(245, 210, 128, 0.12);
    transition: all 0.3s ease;
    height: fit-content;

    &:hover {
      box-shadow: 0 8px 32px rgba(245, 210, 128, 0.18);
      transform: translateY(-2px);
    }

    .ant-card-head {
      background: linear-gradient(135deg, @primary-color 0%, @primary-light 100%);
      border-bottom: 1px solid rgba(245, 210, 128, 0.3);
      border-radius: 16px 16px 0 0;
      padding: 16px 24px;
    }

    .ant-card-body {
      padding: 20px 24px;
    }
  }
}

// 右下：统计信息区
.wrapper_summary_side {
  grid-column: 2;
  grid-row: 2;

  .ant-card {
    border-radius: 16px;
    border: 1px solid rgba(245, 210, 128, 0.3);
    box-shadow: 0 6px 24px rgba(245, 210, 128, 0.12);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 32px rgba(245, 210, 128, 0.18);
      transform: translateY(-2px);
    }

    .ant-card-head {
      background: linear-gradient(135deg, @primary-dark 0%, @primary-color 100%);
      border-bottom: 1px solid rgba(245, 210, 128, 0.3);
      border-radius: 16px 16px 0 0;
      padding: 16px 24px;

      .ant-card-head-title {
        color: white;
        font-weight: 600;
      }
    }

    .ant-card-body {
      padding: 20px 24px;
    }
  }
}

.wrapper_right_box {
  transition: all 1s;
}

// 细节
.preview_title {
  font-weight: bold;
  color: @primary-dark;
  font-size: 16px;
}

.wrapper_params_input {
  width: 100%;
  border-radius: 8px;
  border-color: rgba(245, 210, 128, 0.3);
  transition: all 0.3s ease;

  &:focus, &:hover {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px rgba(245, 210, 128, 0.2);
  }

  .ant-input-number-input {
    border-radius: 8px;
  }

  .ant-select-selector {
    border-radius: 8px !important;
    border-color: rgba(245, 210, 128, 0.3) !important;

    &:hover, &:focus {
      border-color: @primary-color !important;
      box-shadow: 0 0 0 2px rgba(245, 210, 128, 0.2) !important;
    }
  }
}

// 按钮悬停效果
.ant-btn-primary {
  &:hover, &:focus {
    background: linear-gradient(135deg, rgb(139, 117, 61) 0%, rgb(245, 210, 128) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 210, 128, 0.6) !important;
  }

  &:active {
    transform: translateY(0);
  }
}

// 标签样式优化
.ant-tag {
  border-radius: 6px;
  font-weight: 500;
  border: none;

  &.ant-tag-blue {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    color: white;
  }

  &.ant-tag-orange {
    background: linear-gradient(135deg, #fa8c16, #ffa940);
    color: white;
  }

  &.ant-tag-green {
    background: linear-gradient(135deg, #52c41a, #73d13d);
    color: white;
  }

  &.ant-tag-purple {
    background: linear-gradient(135deg, #722ed1, #9254de);
    color: white;
  }

  &.ant-tag-cyan {
    background: linear-gradient(135deg, #13c2c2, #36cfc9);
    color: white;
  }
}

.middle_line_space {
  padding: 8px 0;
  color: @text-color;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.5;
}

.text_line_space {
  padding: 8px 0;
  color: @text-color;
  font-weight: 600;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

// 响应式设计
@media (max-width: 1400px) {
  .flex_wrapper {
    grid-template-columns: 1fr 350px;
    gap: 20px;
    padding: 20px;
  }
}

@media (max-width: 1200px) {
  .flex_wrapper {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 16px;
    padding: 16px;
    margin: 12px;
  }

  .wrapper_left_side {
    grid-column: 1;
    grid-row: 1;
  }

  .wrapper_right_side {
    grid-column: 1;
    grid-row: 2;
  }

  .wrapper_summary_side {
    grid-column: 1;
    grid-row: 3;
  }
}

@media (max-width: 768px) {
  .flex_wrapper {
    padding: 12px;
    margin: 8px;
    border-radius: 12px;
  }

  .wrapper_left_side,
  .wrapper_right_side,
  .wrapper_summary_side {
    .ant-card {
      border-radius: 12px;

      .ant-card-head {
        border-radius: 12px 12px 0 0;
        padding: 12px 16px;
      }

      .ant-card-body {
        padding: 16px;
      }
    }
  }

  .wrapper_left_side .ant-card .ant-card-body {
    max-height: 400px;
  }
}

@media (max-width: 480px) {
  .flex_wrapper {
    padding: 8px;
    margin: 4px;
  }

  .middle_line_space {
    font-size: 13px;
    padding: 6px 0;
  }

  .text_line_space {
    font-size: 14px;
    padding: 6px 0;
  }
}
