// 主题颜色定义
@primary-color: rgb(245, 210, 128);
@primary-light: rgb(255, 248, 220);
@primary-dark: rgb(139, 117, 61);
@text-color: rgb(51, 51, 51);

// 主容器布局
.main_container {
  background: linear-gradient(135deg, @primary-light 0%, #ffffff 100%);
  border-radius: 16px;
  padding: 20px;
  margin: 10px;
  box-shadow: 0 8px 32px rgba(245, 210, 128, 0.1);
}

// 顶部控制面板区域
.control_panel_wrapper {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;

  @media (max-width: 1200px) {
    flex-direction: column;
  }
}

.control_panel_left {
  flex: 2;

  .ant-card {
    border-radius: 12px;
    border: 1px solid rgba(245, 210, 128, 0.3);
    box-shadow: 0 4px 20px rgba(245, 210, 128, 0.1);
    height: 100%;

    .ant-card-head {
      background: linear-gradient(135deg, @primary-light 0%, rgba(255, 255, 255, 0.8) 100%);
      border-bottom: 1px solid rgba(245, 210, 128, 0.3);
      border-radius: 12px 12px 0 0;
    }
  }
}

.control_panel_right {
  flex: 1;

  .ant-card {
    border-radius: 12px;
    border: 1px solid rgba(245, 210, 128, 0.3);
    box-shadow: 0 4px 20px rgba(245, 210, 128, 0.1);
    height: 100%;

    .ant-card-head {
      background: linear-gradient(135deg, @primary-light 0%, rgba(255, 255, 255, 0.8) 100%);
      border-bottom: 1px solid rgba(245, 210, 128, 0.3);
      border-radius: 12px 12px 0 0;
    }
  }
}

// 底部预览区域
.preview_wrapper {
  display: flex;
  gap: 20px;
  align-items: stretch; // 确保子元素高度一致

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.preview_left {
  flex: 1;
  display: flex;
  flex-direction: column;

  .ant-card {
    border-radius: 12px;
    border: 1px solid rgba(245, 210, 128, 0.3);
    box-shadow: 0 4px 20px rgba(245, 210, 128, 0.1);
    margin-bottom: 20px;
    height: 500px; // 固定高度
    display: flex;
    flex-direction: column;

    .ant-card-head {
      background: linear-gradient(135deg, @primary-light 0%, rgba(255, 255, 255, 0.8) 100%);
      border-bottom: 1px solid rgba(245, 210, 128, 0.3);
      border-radius: 12px 12px 0 0;
      flex-shrink: 0;
    }

    .ant-card-body {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
    }
  }
}

.preview_right {
  flex: 1;
  display: flex;
  flex-direction: column;

  .ant-card {
    border-radius: 12px;
    border: 1px solid rgba(245, 210, 128, 0.3);
    box-shadow: 0 4px 20px rgba(245, 210, 128, 0.1);
    height: 500px; // 固定高度，与左侧一致
    display: flex;
    flex-direction: column;

    .ant-card-head {
      background: linear-gradient(135deg, @primary-light 0%, rgba(255, 255, 255, 0.8) 100%);
      border-bottom: 1px solid rgba(245, 210, 128, 0.3);
      border-radius: 12px 12px 0 0;
      flex-shrink: 0;
    }

    .ant-card-body {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
    }
  }
}

// 参数设置区域优化
.params_grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.param_item {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .param_label {
    font-weight: 500;
    color: @text-color;
    font-size: 14px;
  }
}

// 统计信息卡片优化
.stats_grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.stat_item {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(245, 210, 128, 0.2);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(245, 210, 128, 0.1);
    border-color: @primary-color;
    transform: translateY(-2px);
  }

  .stat_label {
    font-size: 12px;
    color: @text-color;
    margin-bottom: 4px;
  }

  .stat_value {
    font-size: 18px;
    font-weight: 600;
    color: @primary-dark;
  }
}

// 细节样式
.preview_title {
  font-weight: bold;
  color: @primary-dark;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.wrapper_params_input {
  width: 100%;
  border-radius: 8px;
  border-color: rgba(245, 210, 128, 0.3);
  transition: all 0.3s ease;

  &:focus, &:hover {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px rgba(245, 210, 128, 0.2);
  }
}

.middle_line_space {
  padding: 5px 0;
  color: @text-color;
  font-weight: 500;
}

// 按钮样式优化
.ant-btn-primary {
  background: linear-gradient(135deg, @primary-color 0%, rgb(200, 170, 100) 100%);
  border-color: @primary-color;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover, &:focus {
    background: linear-gradient(135deg, rgb(200, 170, 100) 0%, @primary-color 100%);
    border-color: @primary-dark;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 210, 128, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

// Switch 组件样式
.ant-switch-checked {
  background-color: @primary-color !important;
}

// Select 组件样式
.ant-select-selector {
  border-radius: 8px !important;
  border-color: rgba(245, 210, 128, 0.3) !important;

  &:hover, &:focus {
    border-color: @primary-color !important;
    box-shadow: 0 0 0 2px rgba(245, 210, 128, 0.2) !important;
  }
}

// Radio 组件样式
.ant-radio-checked .ant-radio-inner {
  border-color: @primary-color !important;
  background-color: @primary-color !important;
}

.ant-radio:hover .ant-radio-inner {
  border-color: @primary-color !important;
}

// 响应式设计
@media (max-width: 1400px) {
  .params_grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

@media (max-width: 1200px) {
  .stats_grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }

  .params_grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  }
}

@media (max-width: 768px) {
  .main_container {
    padding: 16px;
    margin: 8px;
  }

  .stats_grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .params_grid {
    grid-template-columns: 1fr;
  }
}
