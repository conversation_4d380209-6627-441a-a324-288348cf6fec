import React from 'react';
import { connect } from 'umi';
import { <PERSON><PERSON>, Descriptions, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Header, Popconfirm, Progress, Tag } from 'antd';
import { myEmptyStatus, renderLoading } from '../../layouts/commonComponents';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import style from './index.less'

// 注册必要的组件
echarts.use([
  <PERSON><PERSON><PERSON>,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer
]);
import { delay } from '../../utils/myUtils';

class ReportDrawer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {}
  }

  initData = async () => {
  };

  componentWillMount() {
    this.initData().then(() => null)
  }

  // 难度数量统计
  getOption1 = () => {
    let Difficulty1 = 0;
    let Difficulty2 = 0;
    let Difficulty3 = 0;
    let Difficulty4 = 0;
    let Difficulty5 = 0;
    this.props.testPaperReportQuestionList.forEach(item => {
      if (item.difficulty === 1) Difficulty1++;
      else if (item.difficulty === 2) Difficulty2++;
      else if (item.difficulty === 3) Difficulty3++;
      else if (item.difficulty === 4) Difficulty4++;
      else if (item.difficulty === 5) Difficulty5++;
    });

    // 难度等级金黄色系配色
    const difficultyColors = [
      'rgb(255, 248, 220)',  // 难度1 - 极浅金色
      'rgb(255, 235, 180)',  // 难度2 - 浅金色
      'rgb(245, 210, 128)',  // 难度3 - 主金黄色
      'rgb(200, 170, 100)',  // 难度4 - 中金色
      'rgb(139, 117, 61)'    // 难度5 - 深金色
    ];

    return {
      color: difficultyColors,
      tooltip: {
        trigger: 'item',
        formatter: '{b} : {c}题 ({d}%)',
        backgroundColor: 'rgba(255, 248, 220, 0.9)',
        borderColor: 'rgb(245, 210, 128)',
        textStyle: {
          color: 'rgb(139, 117, 61)'
        }
      },
      series: [
        {
          name: '难度',
          type: 'pie',
          radius: [0, '50%'],
          label: {
            position: 'outside',
            fontSize: 10,
            color: 'rgb(139, 117, 61)'
          },
          labelLine: {
            length: 10,
            length2: 15,
            smooth: true,
            lineStyle: {
              color: 'rgb(245, 210, 128)'
            }
          },
          data: [
            {value: Difficulty1, name: '难度1', itemStyle: {color: difficultyColors[0]}},
            {value: Difficulty2, name: '难度2', itemStyle: {color: difficultyColors[1]}},
            {value: Difficulty3, name: '难度3', itemStyle: {color: difficultyColors[2]}},
            {value: Difficulty4, name: '难度4', itemStyle: {color: difficultyColors[3]}},
            {value: Difficulty5, name: '难度5', itemStyle: {color: difficultyColors[4]}}
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(245, 210, 128, 0.5)'
            }
          }
        }
      ]
    }
  };

  // 题目类型数量
  getOption2 = () => {
    let TKTCount = 0;
    let XZTCount = 0;
    let PDTCount = 0;
    let JDTCount = 0;
    this.props.testPaperReportQuestionList.forEach(item => {
      if (item.topic_type === '填空题') TKTCount++;
      else if (item.topic_type === '选择题') XZTCount++;
      else if (item.topic_type === '判断题') PDTCount++;
      else if (item.topic_type === '程序阅读题' || item.topic_type === '程序设计题') JDTCount++;
    });

    // 题型金黄色系配色
    const typeColors = [
      'rgb(245, 210, 128)',  // 填空题 - 主金黄色
      'rgb(255, 235, 180)',  // 判断题 - 浅金色
      'rgb(139, 117, 61)',   // 选择题 - 深金色
      'rgb(200, 170, 100)'   // 程序题 - 中金色
    ];

    return {
      color: typeColors,
      tooltip: {
        trigger: 'item',
        formatter: '{b}数量 : {c} ({d}%)',
        backgroundColor: 'rgba(255, 248, 220, 0.9)',
        borderColor: 'rgb(245, 210, 128)',
        textStyle: {
          color: 'rgb(139, 117, 61)'
        }
      },
      series: [
        {
          name: '数量',
          type: 'pie',
          radius: [0, '50%'],
          label: {
            position: 'outside',
            fontSize: 10,
            color: 'rgb(139, 117, 61)'
          },
          labelLine: {
            length: 10,
            length2: 15,
            smooth: true,
            lineStyle: {
              color: 'rgb(245, 210, 128)'
            }
          },
          data: [
            {value: TKTCount, name: '填空题', itemStyle: {color: typeColors[0]}},
            {value: XZTCount, name: '判断题', itemStyle: {color: typeColors[1]}},
            {value: PDTCount, name: '选择题', itemStyle: {color: typeColors[2]}},
            {value: JDTCount, name: '程序阅读题/程序设计题', itemStyle: {color: typeColors[3]}}
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(245, 210, 128, 0.5)'
            }
          }
        }
      ]
    }
  };

  // 单元题目统计
  getOption3 = () => {
    let _total = {};
    this.props.testPaperReportQuestionList.forEach(item => {
      if (_total[item.label_1] === undefined) {
        _total[item.label_1] = 0
      } else {
        _total[item.label_1]++
      }
    });

    // 知识点金黄色系配色（动态生成）
    const generateColors = (count) => {
      const baseColors = [
        'rgb(245, 210, 128)',  // 主金黄色
        'rgb(255, 235, 180)',  // 浅金色
        'rgb(139, 117, 61)',   // 深金色
        'rgb(200, 170, 100)',  // 中金色
        'rgb(255, 220, 150)',  // 亮金色
        'rgb(180, 150, 80)',   // 暗金色
        'rgb(255, 240, 200)',  // 极浅金色
        'rgb(160, 135, 70)'    // 更深金色
      ];
      const colors = [];
      for (let i = 0; i < count; i++) {
        colors.push(baseColors[i % baseColors.length]);
      }
      return colors;
    };

    const dataKeys = Object.keys(_total);
    const colors = generateColors(dataKeys.length);

    return {
      color: colors,
      tooltip: {
        trigger: 'item',
        formatter: '{b} : {c} ({d}%)',
        backgroundColor: 'rgba(255, 248, 220, 0.9)',
        borderColor: 'rgb(245, 210, 128)',
        textStyle: {
          color: 'rgb(139, 117, 61)'
        }
      },
      series: [
        {
          name: '数量',
          type: 'pie',
          radius: [0, '50%'],
          label: {
            position: 'outside',
            fontSize: 10,
            color: 'rgb(139, 117, 61)'
          },
          labelLine: {
            length: 10,
            length2: 15,
            smooth: true,
            lineStyle: {
              color: 'rgb(245, 210, 128)'
            }
          },
          data: dataKeys.map((item, index) => {
            return {
              value: _total[item],
              name: item,
              itemStyle: {color: colors[index]}
            }
          }),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(245, 210, 128, 0.5)'
            }
          }
        }
      ]
    }
  };

  // 取试卷名字
  getTestPaperName = () => {
    if (this.props.testPaperReportQuestionList.length <= 0) return null
    if (this.props.testPaperReportQuestionList[0].test_paper_name === "") {
      return "《未命名试卷》";
    } else {
      return `《${this.props.testPaperReportQuestionList[0].test_paper_name}》`;
    }
  }

  render() {
    return (
      <Drawer title={null}
              placement="right"
              width={800}
              onClose={this.props.close}
              visible={this.props.visible}
      >
        <PageHeader title={`${this.getTestPaperName()}试卷出题历史记录`} subTitle={'详细记录'}/>

        <Divider orientation='left' style={{fontWeight: 'bold'}}>总体难度{this.props.reportDifficulty}</Divider>
        <div className={style.flex_middle}>
          <span className={style.text_two_side_padding}>难度1</span>
          <Progress percent={(this.props.reportDifficulty / 5 * 100).toFixed(2)}
                    strokeColor={{ '0%': '#00ff00', '100%': '#901300', }}
                    showInfo={false}
                    style={{width: '60%'}}
          />
          <span className={style.text_two_side_padding}>难度5</span>
        </div>

        <Divider orientation='left' style={{fontWeight: 'bold'}}>本试卷共{this.props.testPaperReportQuestionList.length}题</Divider>
        {
          this.props.testPaperReportQuestionList.length > 0 ?
            this.props.testPaperReportQuestionList.map((item, index) => {
              return <Descriptions span={1} key={index}>
                <Descriptions.Item contentStyle={{fontSize: "0.8em", color: "gray"}}>
                  <Tag color="magenta">第{index + 1}题（{item.score}分）</Tag>
                  {item.topic}
                </Descriptions.Item>
              </Descriptions>
            }) :
            myEmptyStatus("无数据", "200px")
        }

        <Divider orientation='left' style={{fontWeight: 'bold'}}>难度分布</Divider>
        {this.props.visible ? <ReactEcharts option={this.getOption1()} /> : null}

        <Divider orientation='left' style={{fontWeight: 'bold'}}>题型分布</Divider>
        <ReactEcharts option={this.getOption2()} />

        <Divider orientation='left' style={{fontWeight: 'bold'}}>知识点分布</Divider>
        <ReactEcharts option={this.getOption3()} />

        <Divider />

      </Drawer>
    )
  }

}
function mapStateToProps({ questionGenHistory }) {
  const { testPaperGenHistories, testPaperReportQuestionList, reportDifficulty } = questionGenHistory;
  return { testPaperGenHistories, testPaperReportQuestionList, reportDifficulty };
}

export default connect(mapStateToProps)(ReportDrawer);
