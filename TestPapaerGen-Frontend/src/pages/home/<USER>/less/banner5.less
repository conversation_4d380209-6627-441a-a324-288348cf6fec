@import './custom.less';

@banner5: banner5;

.@{banner5} {
  // 如果在第一屏且导航位置为 relative, 一屏为 height: calc(~"100vh - 64px");
  width: 100%;
  height: calc(100vh - 64px);
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 248, 248, 0.9) 100%);
  overflow: hidden;

  // 添加装饰性背景
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(245, 210, 128, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(245, 210, 128, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }

  & &-page {
    padding: 40px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: calc(100% - 40px);
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 40px;
    padding-right: 40px;
  }

  &-title {
    font-size: 56px;
    line-height: 1.2;
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 800;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.5px;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    &-wrapper {
      flex: 0 0 45%;
      max-width: 500px;
      position: relative;
      font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      z-index: 2;
      // 添加背景卡片效果提高可读性
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 40px;
      border: 1px solid rgba(245, 210, 128, 0.3);
      box-shadow: 0 8px 32px rgba(245, 210, 128, 0.2);
    }
  }

  &-explain {
    color: #34495e;
    font-size: 22px;
    margin-bottom: 20px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    line-height: 1.4;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  &-content {
    font-size: 16px;
    color: #5a6c7d;
    margin-bottom: 40px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    line-height: 1.6;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  &-button-wrapper {
    .@{banner5}-button {
      background: linear-gradient(135deg, @primary-color 0%, @primary-dark 100%);
      color: #ffffff;
      box-shadow: 0 6px 20px rgba(245, 210, 128, 0.4);
      border-radius: 30px;
      border: 2px solid @primary-color;
      padding: 0 48px;
      line-height: 52px;
      height: 56px;
      font-size: 18px;
      font-weight: 700;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      letter-spacing: 0.5px;
      text-transform: none;
      position: relative;
      overflow: hidden;

      // 添加光泽效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
      }

      &:hover, &:focus {
        transform: translateY(-4px) scale(1.02);
        box-shadow: 0 12px 30px rgba(245, 210, 128, 0.6);
        background: linear-gradient(135deg, @primary-dark 0%, @primary-color 100%);
        color: #ffffff;
        border-color: @primary-dark;

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(-2px) scale(1.01);
        transition: all 0.1s ease;
      }
    }
  }

  &-image {
    overflow: hidden;
    flex: 0 0 50%;
    max-width: 600px;
    position: relative;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(245, 210, 128, 0.3);
    border: 3px solid rgba(245, 210, 128, 0.2);
    margin-left: 40px;

    img {
      transition: transform 0.3s ease;
      border-radius: 17px;
      width: 100%;
      height: auto;
      display: block;
    }

    &:hover img {
      transform: scale(1.02);
    }
  }
}

@media screen and (max-width: 767px) {
  .@{banner5} {
    min-height: 100vh;
    height: auto;

    & &-page.home-page {
      flex-direction: column;
      padding: 20px;
      height: auto;
      min-height: calc(100vh - 40px);
      justify-content: center;
    }

    &-title-wrapper {
      text-align: center;
      flex: none;
      width: 100%;
      margin: 0 0 30px 0;
      padding: 30px 20px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      border: 1px solid rgba(245, 210, 128, 0.3);
      box-shadow: 0 8px 32px rgba(245, 210, 128, 0.2);
    }

    &-title {
      font-size: 36px;
      font-weight: 800;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      letter-spacing: -0.3px;
      margin-bottom: 12px;
      color: #2c3e50;
    }

    &-explain {
      font-size: 18px;
      font-weight: 600;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      margin-bottom: 16px;
      color: #34495e;
    }

    &-content {
      font-size: 15px;
      font-weight: 500;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      line-height: 1.5;
      margin-bottom: 32px;
      color: #5a6c7d;
    }

    &-image {
      flex: none;
      width: 100%;
      max-width: none;
      margin: 0;
    }
  }
}
