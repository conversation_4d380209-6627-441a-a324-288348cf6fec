@import './custom.less';

@banner5: banner5;

.@{banner5} {
  // 如果在第一屏且导航位置为 relative, 一屏为 height: calc(~"100vh - 64px");
  width: 100%;
  height: calc(100vh - 64px);
  position: relative;
  background: linear-gradient(135deg, @primary-light 0%, @primary-color 30%, @primary-dark 100%);
  overflow: hidden;

  // 添加装饰性背景
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }

  & &-page {
    padding: 1px 0 0;
  }

  &-title {
    font-size: 56px;
    line-height: 1.2;
    color: @text-light;
    margin-bottom: 16px;
    font-weight: 800;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.5px;
    // 移除模糊的渐变文字效果，使用清晰的纯色
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    &-wrapper {
      width: 45%;
      max-width: 500px;
      position: relative;
      top: 25%;
      left: 32px;
      font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      z-index: 2;
      // 添加背景卡片效果提高可读性
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 40px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }
  }

  &-explain {
    color: @text-light;
    font-size: 22px;
    margin-bottom: 20px;
    font-weight: 600;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.4;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    opacity: 0.95;
  }

  &-content {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    font-weight: 500;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    line-height: 1.6;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    opacity: 0.9;
  }

  &-button-wrapper {
    .@{banner5}-button {
      background: linear-gradient(135deg, @text-light 0%, rgba(255, 255, 255, 0.95) 100%);
      color: @primary-dark;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
      border-radius: 30px;
      border: 2px solid rgba(255, 255, 255, 0.4);
      padding: 0 48px;
      line-height: 52px;
      height: 56px;
      font-size: 18px;
      font-weight: 700;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(15px);
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      letter-spacing: 0.5px;
      text-transform: none;
      position: relative;
      overflow: hidden;

      // 添加光泽效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
      }

      &:hover, &:focus {
        transform: translateY(-4px) scale(1.02);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.35);
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, @text-light 100%);
        color: @primary-dark;
        border-color: rgba(255, 255, 255, 0.6);

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(-2px) scale(1.01);
        transition: all 0.1s ease;
      }
    }
  }

  &-image {
    overflow: hidden;
    width: 55%;
    max-width: 710px;
    position: absolute;
    top: 15%;
    right: 24px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border: 3px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);

    img {
      transition: transform 0.3s ease;
      border-radius: 17px;
    }

    &:hover img {
      transform: scale(1.02);
    }
  }
}

@media screen and (max-width: 767px) {
  .@{banner5} {
    min-height: 500px;

    & &-page.home-page {
      padding-top: 1px;
      padding-bottom: 0;
    }

    &-title-wrapper {
      text-align: center;
      position: static;
      width: 90%;
      margin: 40px auto 0;
      left: 0;
      top: 0;
      padding: 30px 20px;
      background: rgba(255, 255, 255, 0.08);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    &-title {
      font-size: 36px;
      font-weight: 800;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      letter-spacing: -0.3px;
      margin-bottom: 12px;
    }

    &-explain {
      font-size: 18px;
      font-weight: 600;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      margin-bottom: 16px;
    }

    &-content {
      font-size: 15px;
      font-weight: 500;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      line-height: 1.5;
      margin-bottom: 32px;
    }

    &-image {
      position: static;
      width: 100%;
      top: 0;
      right: 0;
      margin: 32px 0 24px;
    }
  }
}
