// 首页样式优化
@import "./homeTemplate/less/custom.less";

.homeWrapper {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, @primary-light 0%, rgba(255, 255, 255, 0.95) 100%);

  // 确保首页内容与主题一致
  :global(.home-page-wrapper) {
    background: transparent;
  }

  // 优化首页各个组件的背景
  :global(.banner5-wrapper),
  :global(.feature1-wrapper),
  :global(.feature2-wrapper),
  :global(.feature4-wrapper) {
    background: transparent !important;
  }

  // 确保卡片和组件使用主题色
  :global(.ant-card) {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(245, 210, 128, 0.3);
    box-shadow: 0 4px 12px rgba(245, 210, 128, 0.1);

    &:hover {
      box-shadow: 0 6px 16px rgba(245, 210, 128, 0.2);
    }
  }

  // 按钮样式优化
  :global(.ant-btn-primary) {
    background: linear-gradient(135deg, @primary-color 0%, @primary-dark 100%);
    border-color: @primary-color;

    &:hover {
      background: linear-gradient(135deg, @primary-dark 0%, @primary-color 100%);
      border-color: @primary-dark;
    }
  }
}