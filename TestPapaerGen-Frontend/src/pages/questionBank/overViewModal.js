import React from 'react';
import { connect, history } from 'umi';
import {
  <PERSON>ert,
  Button,
  Form,
  Input,
  Radio,
  Modal,
  Select,
  InputNumber,
  DatePicker,
  message,
  Statistic,
  Divider,
} from 'antd';
import { LockOutlined, LoginOutlined, UserOutlined } from '@ant-design/icons';
import styles from './index.less';
import moment from 'moment';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts/core';
import { <PERSON><PERSON><PERSON>, BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  Pie<PERSON><PERSON>,
  Bar<PERSON>hart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  CanvasRenderer
]);
import { delay } from '../../utils/myUtils';

class OverViewModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
    }
  }

  onCancel = async () => {
    this.props.hide()
  };

  onOk = () => {
    this.onCancel().then()
  };

  // calc
  getEachTopicTypeCount = () => {
    const totalCount = this.props.dataSource.length;
    let TKTCount = 0;
    let XZTCount = 0;
    let PDTCount = 0;
    let CXSJTCount = 0;
    let CXYDTCount = 0;
    this.props.dataSource.forEach((item, index) => {
      if (item.topic_type === '填空题') TKTCount++;
      else if (item.topic_type === '选择题') XZTCount++;
      else if (item.topic_type === '判断题') PDTCount++;
      else if (item.topic_type === '程序设计题') CXSJTCount++;
      else if (item.topic_type === '程序阅读题') CXYDTCount++;
    });
    return {totalCount, TKTCount, XZTCount, PDTCount, CXSJTCount, CXYDTCount}
  };

  echarts_option_1_pie_chart = () => {
    let data = [];
    // 金黄色系配色方案
    const colors = [
      'rgb(245, 210, 128)',  // 主金黄色
      'rgb(255, 248, 220)',  // 浅米色
      'rgb(139, 117, 61)',   // 深金色
      'rgb(255, 235, 180)',  // 浅金色
      'rgb(200, 170, 100)',  // 中金色
      'rgb(255, 220, 150)',  // 亮金色
      'rgb(180, 150, 80)',   // 暗金色
      'rgb(255, 240, 200)'   // 极浅金色
    ];

    this.props.eachChapterCount?.forEach((item, index) => {
      data.push({
        name: item.label_1,
        value: item.count,
        itemStyle: {
          color: colors[index % colors.length]
        }
      })
    });

    return {
      title: {
        text: '各章节知识点分布（饼图）',
        textStyle: {
          color: 'rgb(139, 117, 61)',
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      color: colors,
      tooltip: {
        trigger: 'item',
        formatter: '{b} : {c}题',
        backgroundColor: 'rgba(255, 248, 220, 0.9)',
        borderColor: 'rgb(245, 210, 128)',
        textStyle: {
          color: 'rgb(139, 117, 61)'
        }
      },
      series: [
        {
          name: '数量',
          type: 'pie',
          radius: [0, '50%'],
          label: {
            position: 'outside',
            fontSize: 10,
            color: 'rgb(139, 117, 61)'
          },
          labelLine: {
            length: 10,
            length2: 15,
            smooth: true,
            lineStyle: {
              color: 'rgb(245, 210, 128)'
            }
          },
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(245, 210, 128, 0.5)'
            }
          }
        }
      ]
    }
  };

  echarts_option_2_pie_chart = () => {
    let data = [];
    const result = this.getEachTopicTypeCount();
    // 为不同题型设置不同的金黄色系颜色
    const typeColors = [
      'rgb(245, 210, 128)',  // 填空题 - 主金黄色
      'rgb(255, 235, 180)',  // 选择题 - 浅金色
      'rgb(139, 117, 61)',   // 判断题 - 深金色
      'rgb(200, 170, 100)',  // 程序设计题 - 中金色
      'rgb(255, 220, 150)'   // 程序阅读题 - 亮金色
    ];

    data.push({name: '填空题', value: result.TKTCount, itemStyle: {color: typeColors[0]}});
    data.push({name: '选择题', value: result.XZTCount, itemStyle: {color: typeColors[1]}});
    data.push({name: '判断题', value: result.PDTCount, itemStyle: {color: typeColors[2]}});
    data.push({name: '程序设计题', value: result.CXSJTCount, itemStyle: {color: typeColors[3]}});
    data.push({name: '程序阅读题', value: result.CXYDTCount, itemStyle: {color: typeColors[4]}});

    return {
      title: {
        text: '各题型数量分布数量统计（饼图）',
        textStyle: {
          color: 'rgb(139, 117, 61)',
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      color: typeColors,
      tooltip: {
        trigger: 'item',
        formatter: '{b} : {c}题',
        backgroundColor: 'rgba(255, 248, 220, 0.9)',
        borderColor: 'rgb(245, 210, 128)',
        textStyle: {
          color: 'rgb(139, 117, 61)'
        }
      },
      series: [
        {
          name: '数量',
          type: 'pie',
          radius: [0, '50%'],
          label: {
            position: 'outside',
            fontSize: 10,
            color: 'rgb(139, 117, 61)'
          },
          labelLine: {
            length: 10,
            length2: 15,
            smooth: true,
            lineStyle: {
              color: 'rgb(245, 210, 128)'
            }
          },
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(245, 210, 128, 0.5)'
            }
          }
        }
      ]
    }
  };

  echarts_option_3_bar_chart = () => {
    let xAxisData = [];
    let seriesData = [];
    this.props.eachScoreCount?.forEach((item, index) => {
      xAxisData.push(item.score);
      seriesData.push(item.count)
    });
    return {
      title: {
        text: '各分值分布统计（柱状图）',
        textStyle: {
          color: 'rgb(139, 117, 61)',
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        name: '每题分值',
        nameTextStyle: {
          color: 'rgb(139, 117, 61)'
        },
        axisLabel: {
          color: 'rgb(139, 117, 61)'
        },
        axisLine: {
          lineStyle: {
            color: 'rgb(245, 210, 128)'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '题目数',
        nameTextStyle: {
          color: 'rgb(139, 117, 61)'
        },
        axisLabel: {
          color: 'rgb(139, 117, 61)'
        },
        axisLine: {
          lineStyle: {
            color: 'rgb(245, 210, 128)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(245, 210, 128, 0.3)'
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}分题的数量 : {c}题',
        backgroundColor: 'rgba(255, 248, 220, 0.9)',
        borderColor: 'rgb(245, 210, 128)',
        textStyle: {
          color: 'rgb(139, 117, 61)'
        }
      },
      series: [{
        data: seriesData,
        type: 'bar',
        showBackground: false,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgb(255, 235, 180)' },
            { offset: 1, color: 'rgb(245, 210, 128)' }
          ])
        },
        backgroundStyle: {
          color: 'rgba(245, 210, 128, 0.1)'
        }
      }]
    }
  };

  // initData
  initData = async () => {
    await this.props.dispatch({ type: 'questionBank/getEachChapterCount' });
    await this.props.dispatch({ type: 'questionBank/getEachScoreCount' });
  };

  // lifeCycle
  componentDidMount() {
    this.initData().then(null);
  }


  render() {

    const renderStat = () => {
      const totalCount = this.props.dataSource.length;
      let TKTCount = 0;
      let XZTCount = 0;
      let PDTCount = 0;
      let CXSJTCount = 0;
      let CXYDTCount = 0;
      this.props.dataSource.forEach((item, index) => {
        if (item.topic_type === '填空题') TKTCount++;
        else if (item.topic_type === '选择题') XZTCount++;
        else if (item.topic_type === '判断题') PDTCount++;
        else if (item.topic_type === '程序设计题') CXSJTCount++;
        else if (item.topic_type === '程序阅读题') CXYDTCount++;
      });
      return <div>
        <div className={styles.row_flex}>
          <Statistic className={styles.statistic_board} title="试题库总数：" value={Number(totalCount)} suffix={'题'} />
          <Divider type="vertical" />
          <Statistic className={styles.statistic_board} title="填空题总数：" value={Number(TKTCount)} suffix={'题'} />
          <Divider type="vertical" />
          <Statistic className={styles.statistic_board} title="选择题总数：" value={Number(XZTCount)} suffix={'题'} />
          <Divider type="vertical" />
          <Statistic className={styles.statistic_board} title="判断题总数：" value={Number(PDTCount)} suffix={'题'} />
          <Divider type="vertical" />
          <Statistic className={styles.statistic_board} title="程序设计题总数：" value={Number(CXSJTCount)} suffix={'题'} />
          <Divider type="vertical" />
          <Statistic className={styles.statistic_board} title="程序阅读题总数：" value={Number(CXYDTCount)} suffix={'题'} />
          <Divider type="vertical" />
        </div>
        <Divider orientation='left' style={{fontWeight: 'bold'}}>题库各类数量统计</Divider>
        <div className={styles.row_flex}>
          <div style={{width: '600px', height: '300px'}}>
            <ReactEcharts option={this.echarts_option_1_pie_chart()} />
          </div>
        </div>
        <div className={styles.row_flex}>
          <div style={{width: '600px', height: '300px'}}>
            <ReactEcharts option={this.echarts_option_2_pie_chart()} />
          </div>
        </div>
        <div className={styles.row_flex}>
          <div style={{width: '600px', height: '300px'}}>
            <ReactEcharts option={this.echarts_option_3_bar_chart()} />
          </div>
        </div>
      </div>
    };

    return <div>
      <Modal
        title="试题库概览"
        centered={true}
        visible={this.props.visible}
        onOk={this.onOk}
        onCancel={this.onCancel}
        footer={null}
        width={'70%'}
      >
        <div>
          {
            renderStat()
          }
        </div>
      </Modal>

    </div>
  }
}

function mapStateToProps({ questionBank }) {
  const { eachChapterCount, eachScoreCount } = questionBank;
  return { eachChapterCount, eachScoreCount };
}

export default connect(mapStateToProps)(OverViewModal);
