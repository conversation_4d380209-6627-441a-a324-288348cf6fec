// 主题颜色定义
@primary-color: rgb(245, 210, 128);
@primary-light: rgb(255, 248, 220);
@primary-dark: rgb(139, 117, 61);
@text-color: rgb(51, 51, 51);
@text-light: rgb(255, 255, 255);

@sidebar-width: 260px;
@header-height: 64px;

// 登录页面专用布局
.loginPageWrapper {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

// 整体布局容器
.layoutWrapper {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

// 侧边栏容器
.sidebarWrapper {
  width: @sidebar-width;
  min-height: 100vh;
  background: linear-gradient(180deg, @primary-color 0%, @primary-dark 100%);
  box-shadow: 2px 0 16px rgba(0, 0, 0, 0.12);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;

  // 添加微妙的纹理效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 30px 30px;
    pointer-events: none;
  }
}

// Logo区域
.logoSection {
  padding: 24px 20px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20%;
    right: 20%;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.6) 50%, transparent 100%);
  }
}

.logoTitle {
  font-size: 20px;
  font-weight: 700;
  color: @text-light;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  letter-spacing: 1.5px;
  margin: 0;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  }
}

// 菜单区域
.menuSection {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

// 主内容区域
.mainWrapper {
  flex: 1;
  margin-left: @sidebar-width;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

// 顶部用户信息栏
.headerWrapper {
  height: @header-height;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%);
  backdrop-filter: blur(20px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 32px;
  position: sticky;
  top: 0;
  z-index: 999;
  border-bottom: 1px solid rgba(245, 210, 128, 0.1);

  // 添加微妙的渐变边框
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(245, 210, 128, 0.3) 50%, transparent 100%);
  }
}

// 内容容器
.contentWrapper {
  flex: 1;
  padding: 0;
  background: linear-gradient(135deg, @primary-light 0%, rgba(255, 255, 255, 0.95) 100%);
  min-height: calc(100vh - @header-height);
  position: relative;

  // 添加微妙的纹理背景
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 25% 25%, rgba(245, 210, 128, 0.05) 1px, transparent 1px),
      radial-gradient(circle at 75% 75%, rgba(245, 210, 128, 0.03) 1px, transparent 1px);
    background-size: 50px 50px, 30px 30px;
    pointer-events: none;
    z-index: 0;
  }

  // 确保内容在纹理之上
  > * {
    position: relative;
    z-index: 1;
  }
}

// 用户信息区域
.userInfo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.userProfile {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(245, 210, 128, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  &:hover {
    background: rgba(245, 210, 128, 0.15);
    border-color: rgba(245, 210, 128, 0.4);
    box-shadow: 0 4px 16px rgba(245, 210, 128, 0.2);
    transform: translateY(-1px);
  }
}

.userAvatar {
  background: linear-gradient(135deg, @primary-color 0%, @primary-dark 100%);
  border: 2px solid rgba(245, 210, 128, 0.4);
  box-shadow: 0 2px 8px rgba(245, 210, 128, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(245, 210, 128, 0.4);
  }
}

.username {
  font-size: 14px;
  font-weight: 600;
  color: @text-color;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.3s ease;
}

// 自定义菜单样式
.customMenu {
  background: transparent !important;
  border: none !important;

  // 菜单项样式
  .ant-menu-item {
    color: @text-light !important;
    font-weight: 500;
    font-size: 15px;
    margin: 4px 16px !important;
    padding: 12px 16px !important;
    height: auto !important;
    line-height: 1.4 !important;
    border-radius: 12px !important;
    transition: all 0.3s ease;
    border: none !important;
    position: relative;

    &:hover {
      color: @primary-dark !important;
      background: rgba(255, 255, 255, 0.9) !important;
      transform: translateX(4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.ant-menu-item-selected {
      color: @primary-dark !important;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 248, 220, 0.9) 100%) !important;
      transform: translateX(6px);
      box-shadow: 0 6px 16px rgba(245, 210, 128, 0.3);
      border-left: 4px solid @primary-color !important;

      &::after {
        display: none;
      }

      // 添加金色光晕效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(245, 210, 128, 0.1) 0%, transparent 100%);
        border-radius: 12px;
        pointer-events: none;
      }
    }

    // 图标样式
    .anticon {
      font-size: 18px;
      margin-right: 12px;
      transition: all 0.3s ease;
    }

    // 选中状态图标颜色
    &.ant-menu-item-selected .anticon {
      color: @primary-color !important;
    }
  }

  // 垂直菜单样式
  &.ant-menu-vertical {
    .ant-menu-item {
      width: calc(100% - 32px);

      &::after {
        display: none;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  @sidebar-width-tablet: 220px;

  .sidebarWrapper {
    width: @sidebar-width-tablet;
  }

  .mainWrapper {
    margin-left: @sidebar-width-tablet;
  }

  .logoTitle {
    font-size: 18px;
  }

  .customMenu {
    .ant-menu-item {
      margin: 4px 12px !important;
      padding: 10px 12px !important;
      font-size: 14px;

      .anticon {
        font-size: 16px;
        margin-right: 10px;
      }
    }
  }
}

@media (max-width: 768px) {
  @sidebar-width-mobile: 180px;

  .sidebarWrapper {
    width: @sidebar-width-mobile;
    transform: translateX(-100%);
    transition: transform 0.3s ease;

    &.mobile-open {
      transform: translateX(0);
    }
  }

  .mainWrapper {
    margin-left: 0;
  }

  .logoSection {
    padding: 16px;
  }

  .logoTitle {
    font-size: 16px;
  }

  .customMenu {
    .ant-menu-item {
      margin: 2px 8px !important;
      padding: 8px 12px !important;
      font-size: 13px;

      .anticon {
        font-size: 16px;
        margin-right: 8px;
      }
    }
  }

  .headerWrapper {
    padding: 0 16px;
  }

  .contentWrapper {
    padding: 16px;
  }

  .username {
    display: none;
  }
}

@media (max-width: 576px) {
  .customMenu {
    .ant-menu-item {
      margin: 2px 4px !important;
      padding: 8px !important;
      text-align: center;

      span {
        display: none;
      }

      .anticon {
        margin-right: 0;
        font-size: 18px;
      }
    }
  }

  .logoTitle {
    font-size: 14px;
  }

  .headerWrapper {
    padding: 0 12px;
  }

  .contentWrapper {
    padding: 12px;
  }
}
