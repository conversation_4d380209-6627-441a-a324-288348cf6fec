// 主题颜色定义
@primary-color: rgb(245, 210, 128);
@primary-light: rgb(255, 248, 220);
@primary-dark: rgb(139, 117, 61);
@text-color: rgb(51, 51, 51);
@text-light: rgb(255, 255, 255);
@sidebar-width: 240px;

// 整体布局容器
.layoutWrapper {
  display: flex;
  min-height: 100vh;
  background: #f0f2f5;
}

// 侧边栏容器
.sidebarWrapper {
  width: @sidebar-width;
  background: linear-gradient(180deg, @primary-color 0%, @primary-dark 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

// Logo区域
.logoSection {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;

  .logoTitle {
    color: @text-light;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 1px;
  }
}

// 菜单区域
.menuSection {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

// 主内容区域容器
.mainWrapper {
  flex: 1;
  margin-left: @sidebar-width;
  display: flex;
  flex-direction: column;
}

// 顶部用户信息栏
.headerWrapper {
  height: 60px;
  background: @text-light;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 24px;
  position: relative;
  z-index: 999;
}

// 用户信息组件
.userInfo {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(245, 210, 128, 0.1);
  border: 1px solid rgba(245, 210, 128, 0.3);

  &:hover {
    background: rgba(245, 210, 128, 0.2);
    border-color: @primary-color;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 210, 128, 0.3);
  }

  .userIcon {
    font-size: 16px;
    color: @primary-dark;
  }

  .username {
    color: @text-color;
    font-weight: 500;
    font-size: 14px;
  }

  .logoutText {
    color: @primary-dark;
    font-size: 12px;
    opacity: 0.8;
  }
}

// 内容容器
.contentWrapper {
  flex: 1;
  background: linear-gradient(135deg, @primary-light 0%, #ffffff 100%);
  padding: 0;
  overflow-y: auto;
}

// 自定义菜单样式
.customMenu {
  background: transparent !important;
  border: none !important;
  border-right: none !important;

  // 菜单项样式
  .ant-menu-item {
    color: @text-light !important;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    margin: 4px 16px;
    border-radius: 8px;
    height: 48px;
    line-height: 48px;
    border: none !important;

    &:hover {
      color: @text-color !important;
      background: rgba(255, 255, 255, 0.2) !important;
      border-left: 4px solid @text-light !important;
      transform: translateX(4px);
    }

    &.ant-menu-item-selected {
      color: @text-color !important;
      background: rgba(255, 255, 255, 0.3) !important;
      border-left: 4px solid @text-light !important;
      transform: translateX(4px);

      &::after {
        display: none;
      }
    }

    // 图标样式
    .anticon {
      font-size: 16px;
      margin-right: 12px;
      min-width: 16px;
    }
  }

  // 垂直菜单特殊样式
  &.ant-menu-vertical {
    .ant-menu-item {
      padding: 0 20px;
      margin: 4px 16px;
      width: calc(100% - 32px);

      &::after {
        display: none;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .sidebarWrapper {
    width: 200px;
  }

  .mainWrapper {
    margin-left: 200px;
  }

  .logoSection .logoTitle {
    font-size: 16px;
  }

  .customMenu .ant-menu-item {
    font-size: 13px;
    height: 44px;
    line-height: 44px;
    margin: 3px 12px;

    .anticon {
      font-size: 14px;
      margin-right: 10px;
    }
  }
}

@media (max-width: 768px) {
  .sidebarWrapper {
    width: 180px;
  }

  .mainWrapper {
    margin-left: 180px;
  }

  .headerWrapper {
    height: 50px;
    padding: 0 16px;
  }

  .userInfo {
    padding: 6px 12px;

    .username {
      font-size: 13px;
    }

    .logoutText {
      font-size: 11px;
    }
  }

  .customMenu .ant-menu-item {
    font-size: 12px;
    height: 40px;
    line-height: 40px;
    margin: 2px 8px;
    padding: 0 12px;

    .anticon {
      font-size: 13px;
      margin-right: 8px;
    }
  }
}

@media (max-width: 576px) {
  .sidebarWrapper {
    width: 60px;

    .logoSection {
      padding: 15px 5px;

      .logoTitle {
        font-size: 12px;
        writing-mode: vertical-rl;
        text-orientation: mixed;
      }
    }
  }

  .mainWrapper {
    margin-left: 60px;
  }

  .customMenu .ant-menu-item {
    margin: 2px 4px;
    padding: 0 8px;
    text-align: center;

    span {
      display: none;
    }

    .anticon {
      margin-right: 0;
      font-size: 16px;
    }
  }

  .userInfo {
    .username {
      display: none;
    }

    .logoutText {
      display: none;
    }
  }
}
