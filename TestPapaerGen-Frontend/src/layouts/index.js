import React from 'react';
import { connect, history } from 'umi';
import { Menu, Avatar, Dropdown } from 'antd';
import {
  HomeOutlined,
  OrderedListOutlined,
  EditOutlined,
  FileAddOutlined,
  HistoryOutlined,
  UserOutlined,
  LoginOutlined,
  LogoutOutlined,
  SettingOutlined
} from '@ant-design/icons';
import styles from './index.less';

class LayoutComponent extends React.Component {
  constructor(props) {
    super(props);
    this.state = {}
  }

  componentWillMount() {
    this.props.dispatch({type: 'loginModel/getLoginStatus'})
  }

  render() {
    const onClick = e => {
      if (history.location.pathname !== e.key) history.push(e.key);
    };

    // 判断是否为登录页面或根路径
    const isLoginPage = history.location.pathname === "/" || history.location.pathname === "/login";
    const isAdminPage = history.location.pathname === "/admin";

    const renderUserInfo = () => {
      if (isLoginPage || isAdminPage) {
        return null;
      }

      const userMenuItems = [
        // {
        //   key: 'profile',
        //   icon: <UserOutlined />,
        //   label: '个人信息',
        // },
        // {
        //   key: 'settings',
        //   icon: <SettingOutlined />,
        //   label: '设置',
        // },
        {
          type: 'divider',
        },
        {
          key: '/login',
          icon: <LogoutOutlined />,
          label: '退出登录',
          onClick: () => onClick({key: "/login"})
        },
      ];

      return (
        <div className={styles.userInfo}>
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={['click']}
          >
            <div className={styles.userProfile}>
              <Avatar
                size="default"
                icon={<UserOutlined />}
                className={styles.userAvatar}
              />
              <span className={styles.username}>{this.props.username}</span>
            </div>
          </Dropdown>
        </div>
      );
    };

    const menu = () => {
      if (isLoginPage) {
        return null;
      }
      else if (isAdminPage) {
        const items = [
          {
            key: "/admin",
            icon: <HomeOutlined />,
            label: "管理员首页"
          }
        ]
        return <Menu items={items} mode="vertical" className={styles.customMenu} onClick={onClick} selectedKeys={[history.location.pathname]} />
      }
      else {
        const items = [
          {
            key: "/home",
            icon: <HomeOutlined />,
            label: "首页"
          },
          {
            key: "/questionBank",
            icon: <OrderedListOutlined />,
            label: "试题库显示"
          },
          {
            key: "/questionEdit",
            icon: <EditOutlined />,
            label: "添加或修改"
          },
          {
            key: "/questionGenerator",
            icon: <FileAddOutlined />,
            label: "组卷功能"
          }
          // {
          //   key: "/questionGenHistory",
          //   icon: <HistoryOutlined />,
          //   label: "出题历史"
          // }
        ]
        return <Menu items={items}
                     mode="vertical"
                     className={styles.customMenu}
                     onClick={onClick}
                     selectedKeys={[history.location.pathname]}
        />
      }
    }

    // 如果是登录页面，使用简单的全屏布局
    if (isLoginPage) {
      return <div className={styles.loginPageWrapper}>
        { this.props.children }
      </div>;
    }

    // 其他页面使用侧边栏布局
    return <div className={styles.layoutWrapper}>
        <div className={styles.sidebarWrapper}>
          <div className={styles.logoSection}>
            <div className={styles.logoTitle}>试题生成系统</div>
          </div>
          <div className={styles.menuSection}>
            {menu()}
          </div>
        </div>
        <div className={styles.mainWrapper}>
          <div className={styles.headerWrapper}>
            {renderUserInfo()}
          </div>
          <div className={styles.contentWrapper}>
            { this.props.children }
          </div>
        </div>
    </div>;
  }
}

function mapStateToProps({ loginModel }) {
  const { username } = loginModel;
  return { username };
}

export default connect(mapStateToProps)(LayoutComponent);
