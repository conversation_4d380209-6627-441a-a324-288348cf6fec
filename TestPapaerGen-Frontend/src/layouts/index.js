import React from 'react';
import { connect, history } from 'umi';
import { Menu } from 'antd';
import {
  HomeOutlined,
  OrderedListOutlined,
  EditOutlined,
  FileAddOutlined,
  HistoryOutlined,
  UserOutlined,
  LoginOutlined
} from '@ant-design/icons';
import styles from './index.less';

class TopMenu extends React.Component {
  constructor(props) {
    super(props);
    this.state = {}
  }

  componentWillMount() {
    this.props.dispatch({type: 'loginModel/getLoginStatus'})
  }

  render() {
    const onClick = e => {
      if (history.location.pathname !== e.key) history.push(e.key);
    };

    const renderUserInfo = () => {
      if (history.location.pathname === "/" || history.location.pathname === "admin") {
        return null;
      }
      return (
        <div className={styles.userInfo} onClick={() => onClick({key: "/login"})}>
          <UserOutlined className={styles.userIcon} />
          <span className={styles.username}>{this.props.username}</span>
          <span className={styles.logoutText}>退出</span>
        </div>
      );
    };

    const menu = () => {
      if (history.location.pathname === "/") {
        return null;
      }
      else if (history.location.pathname === "admin") {
        const items = [
          {
            key: "/admin",
            icon: <HomeOutlined />,
            label: "管理员首页"
          }
        ]
        return <Menu items={items} mode="vertical" className={styles.customMenu} onClick={onClick} selectedKeys={history.location.pathname} />
      }
      else {
        const items = [
          {
            key: "/home",
            icon: <HomeOutlined />,
            label: "首页"
          },
          {
            key: "/questionBank",
            icon: <OrderedListOutlined />,
            label: "试题库显示"
          },
          {
            key: "/questionEdit",
            icon: <EditOutlined />,
            label: "添加或修改"
          },
          {
            key: "/questionGenerator",
            icon: <FileAddOutlined />,
            label: "组卷功能"
          }
          // {
          //   key: "/questionGenHistory",
          //   icon: <HistoryOutlined />,
          //   label: "出题历史"
          // }
        ]
        return <Menu items={items}
                     mode="vertical"
                     className={styles.customMenu}
                     onClick={onClick}
                     selectedKeys={history.location.pathname}
        />
      }
    }

    return <div className={styles.layoutWrapper}>
        <div className={styles.sidebarWrapper}>
          <div className={styles.logoSection}>
            <div className={styles.logoTitle}>试题生成系统</div>
          </div>
          <div className={styles.menuSection}>
            {menu()}
          </div>
        </div>
        <div className={styles.mainWrapper}>
          <div className={styles.headerWrapper}>
            {renderUserInfo()}
          </div>
          <div className={styles.contentWrapper}>
            { this.props.children }
          </div>
        </div>
    </div>;
  }
}

function mapStateToProps({ loginModel }) {
  const { username } = loginModel;
  return { username };
}

export default connect(mapStateToProps)(TopMenu);
