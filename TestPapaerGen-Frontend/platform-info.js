const os = require('os');
const path = require('path');

console.log('🖥️  平台信息检测\n');

const platform = process.platform;
const isWindows = platform === 'win32';
const isMac = platform === 'darwin';
const isLinux = platform === 'linux';

console.log('📋 系统信息:');
console.log(`   操作系统: ${os.type()} ${os.release()}`);
console.log(`   平台: ${platform}`);
console.log(`   架构: ${os.arch()}`);
console.log(`   Node.js: ${process.version}`);
console.log(`   工作目录: ${process.cwd()}`);

console.log('\n🔧 平台特性:');
console.log(`   Windows: ${isWindows ? '✅' : '❌'}`);
console.log(`   macOS: ${isMac ? '✅' : '❌'}`);
console.log(`   Linux: ${isLinux ? '✅' : '❌'}`);

console.log('\n📁 路径信息:');
console.log(`   路径分隔符: "${path.sep}"`);
console.log(`   示例路径: ${path.join('src', 'pages', 'home')}`);

console.log('\n⚙️  推荐配置:');
if (isWindows) {
  console.log('   🔸 MFSU: 禁用 (避免模块解析问题)');
  console.log('   🔸 Host: localhost (避免网络问题)');
  console.log('   🔸 内存: 设置 NODE_OPTIONS=--max-old-space-size=4096');
  console.log('   🔸 启动: 使用 npm run dev:windows');
} else if (isMac) {
  console.log('   🔸 MFSU: 启用 (提升构建速度)');
  console.log('   🔸 Host: 0.0.0.0 (支持网络访问)');
  console.log('   🔸 启动: 使用 npm start');
} else {
  console.log('   🔸 配置: 类似 macOS 设置');
  console.log('   🔸 启动: 使用 npm start');
}

console.log('\n🚀 启动建议:');
if (isWindows) {
  console.log('   1. 运行: start-windows.bat');
  console.log('   2. 或者: npm run dev:windows');
  console.log('   3. 访问: http://localhost:8000');
} else {
  console.log('   1. 运行: npm start');
  console.log('   2. 访问: http://localhost:8000');
}

console.log('\n📝 注意事项:');
if (isWindows) {
  console.log('   ⚠️  可能需要以管理员身份运行');
  console.log('   ⚠️  确保防火墙允许Node.js访问网络');
  console.log('   ⚠️  如遇问题，运行 fix-module-error.bat');
} else {
  console.log('   ✅ 通常无需特殊配置');
  console.log('   ✅ 如遇端口占用，使用: lsof -ti:8000 | xargs kill');
}
