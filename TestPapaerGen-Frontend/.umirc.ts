import { defineConfig } from 'umi';

// 检测操作系统
const isWindows = process.platform === 'win32';

export default defineConfig({
  // 根据操作系统配置MFSU
  mfsu: isWindows ? false : {},
  // cdn目录
  publicPath: "/static/",

  // 跨平台开发服务器配置
  devServer: {
    port: 8000,
    host: isWindows ? 'localhost' : '0.0.0.0',
    open: !isWindows, // Windows上不自动打开浏览器
    https: false,
    writeToDisk: false
  },

  // 按需加载 暂时不需要
  // dynamicImport: {
  //   loading: "@/layouts/loading"
  // },

  // 国际化
  locale: {
    default: 'zh-CN',
    antd: true
  },

  // 主题配置
  theme: {
    '@primary-color': 'rgb(245, 210, 128)',
    '@border-radius-base': '8px',
    '@box-shadow-base': '0 2px 8px rgba(0, 0, 0, 0.15)',
  },
  history: {
    type: 'hash'
  },
  routes: [
    {
      path: '/',
      component: '@/layouts/index',
      routes: [

        // 登陆
        { path: '/',  exact: true, component: '@/pages/login', title: "欢迎使用《组卷系统》" },
        { path: '/login',  exact: true, redirect: '/' },

        // 管理员
        {exact: true, path: '/admin', component: '@/pages/admin', title: "组卷系统-管理员"},

        // 用户
        {exact: true, path: '/home', component: '@/pages/home', title: "组卷系统-首页"},
        {exact: true, path: '/questionBank', component: '@/pages/questionBank', title: "组卷系统-题库"},
        {exact: true, path: '/questionEdit', component: '@/pages/questionEdit', title: "组卷系统-编辑"},
        {exact: true, path: '/questionGenerator', component: '@/pages/questionGenerator', title: "组卷系统-组卷"},
        {exact: true, path: '/questionGenHistory', component: '@/pages/questionGenHistory', title: "组卷系统-历史记录"},
        // {exact: true, path: '/questionManager', component: '@/pages/questionManager', title: "组卷系统"}
      ]
    }
  ],
  plugins: [],
});
