@echo off
echo ========================================
echo   Server Status Check
echo ========================================
echo.

echo [1] Checking if server is running on port 8000...
netstat -an | findstr :8000
if %errorlevel% equ 0 (
    echo ✅ Port 8000 is in use
) else (
    echo ❌ Port 8000 is not in use - server may not be running
)

echo.
echo [2] Checking Node.js processes...
tasklist | findstr node.exe
if %errorlevel% equ 0 (
    echo ✅ Node.js processes found
) else (
    echo ❌ No Node.js processes running
)

echo.
echo [3] Testing local connection...
curl -s http://localhost:8000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Server responds on localhost:8000
) else (
    echo ❌ Cannot connect to localhost:8000
)

echo.
echo [4] Checking firewall/network...
ping -n 1 127.0.0.1 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Localhost connectivity OK
) else (
    echo ❌ Localhost connectivity issue
)

echo.
echo ========================================
echo   Recommendations:
echo ========================================
if not exist "node_modules" (
    echo - Run: npm install
)
echo - Start server: npm run dev:windows
echo - Or use: start-windows.bat
echo - Check browser: http://localhost:8000
echo.
pause
