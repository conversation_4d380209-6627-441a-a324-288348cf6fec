# es5-imcompatible-versions

Collect all uglifyjs bug versions on npm package.

[![NPM version](https://img.shields.io/npm/v/es5-imcompatible-versions.svg?style=flat)](https://npmjs.org/package/es5-imcompatible-versions)
[![Build Status](https://img.shields.io/travis/umijs/es5-imcompatible-versions.svg?style=flat)](https://travis-ci.org/umijs/es5-imcompatible-versions)
[![NPM downloads](http://img.shields.io/npm/dm/es5-imcompatible-versions.svg?style=flat)](https://npmjs.org/package/es5-imcompatible-versions)

## Why is this package?

https://github.com/sorrycc/blog/issues/68

## Questions & Suggestions

Please open an issue [here](https://github.com/umijs/umi/issues?q=is%3Aissue+is%3Aopen+sort%3Aupdated-desc).

## LICENSE

MIT
