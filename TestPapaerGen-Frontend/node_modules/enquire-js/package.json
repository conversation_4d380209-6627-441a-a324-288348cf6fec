{"name": "enquire-js", "version": "0.2.1", "description": "avoid server-side rendering errors.", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prepublish": "babel index.jsx --out-file main.js"}, "repository": {"type": "git", "url": "git+https://github.com/jljsj/enquire-js.git"}, "author": "<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/jljsj/enquire-js/issues"}, "homepage": "https://github.com/jljsj/enquire-js#readme", "dependencies": {"enquire.js": "^2.1.6"}, "devDependencies": {"babel-cli": "^6.6.5", "babel-preset-es2015": "^6.6.0"}}