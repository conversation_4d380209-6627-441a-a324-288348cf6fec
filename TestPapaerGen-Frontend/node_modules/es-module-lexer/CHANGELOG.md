0.7.1
* Fix nested import.meta inside of dynamic import (https://github.com/guybedford/es-module-lexer/pull/80)
* Add missing typings for import assertions (https://github.com/guybedford/es-module-lexer/pull/78, @marvinhagemeister)

0.7.0
* Fix package exports for Node.js 12.18 (https://github.com/guybedford/es-module-lexer/pull/77, @marvinhagemeister)

0.6.0
* Simplify import assertions implementation (https://github.com/guybedford/es-module-lexer/pull/75)

0.5.0
* Add support for import assertions (https://github.com/guybedford/es-module-lexer/pull/74)

0.4.1
* Add TypeScript types (https://github.com/guybedford/es-module-lexer/pull/63, https://github.com/guybedford/es-module-lexer/pull/64 @xiaoxiangmoe)
* Better export detection before bailing on `=` (https://github.com/guybedford/es-module-lexer/pull/66)

0.4.0
* Adds support for .n property with unescaped specifier string (https://github.com/guybedford/es-module-lexer/pull/62)
* Only use atob global in browser environments (https://github.com/guybedford/es-module-lexer/pull/58)

0.3.13
* Fix comment support in export var statements (https://github.com/guybedford/es-module-lexer/pull/35)

0.3.12
* Fix empty export statement handling (https://github.com/guybedford/es-module-lexer/pull/32)
* Add Binaryen optimization passes to reduce file size (https://github.com/guybedford/es-module-lexer/pull/33)

0.3.11
* Fixup parse error column handling (https://github.com/guybedford/es-module-lexer/commit/3979105162c50827af00dc4549944d708896da53)
* Fix non-terminating loop case (https://github.com/guybedford/es-module-lexer/pull/31)

0.3.10
* Better parse errors (https://github.com/guybedford/es-module-lexer/pull/30)
* Handle end offset correctly (https://github.com/guybedford/es-module-lexer/pull/29)

0.3.9
* Better invalid state handling (https://github.com/guybedford/es-module-lexer/pull/28)
* Handle allocations for large numbers of exports (https://github.com/guybedford/es-module-lexer/pull/27)

0.3.8
* Fix template parsing bug (https://github.com/guybedford/es-module-lexer/pull/22)

0.3.7
* Refactoring (https://github.com/guybedford/es-module-lexer/pull/20, https://github.com/guybedford/es-module-lexer/pull/21)

0.3.6
* Fix case where methods named import would be incorrectly reported as dynamic imports (https://github.com/guybedford/es-module-lexer/pull/19)

0.3.5
* Fix Node.js 10 memory grow support for globals without a value getter (https://github.com/guybedford/es-module-lexer/issues/14)

0.3.4
* Use UTF16 encoding for better performance, and removing reliance on TextEncoder (https://github.com/guybedford/es-module-lexer/pull/15)

0.3.3
* Minification improvements
* Fix for TextEncoder global being missing in Node.js 10
* Fix CJS build to end in .cjs extension for modules compatibility

0.3.2
* Fix export declaration parse bugs (https://github.com/guybedford/es-module-lexer/pull/11)

0.3.1
* Fix up the ESM and CJS interfaces to use named exports

0.3.0
* Web Assembly conversion for performance (https://github.com/guybedford/es-module-lexer/pull/7)
* Fix $ characters in templates (https://github.com/guybedford/es-module-lexer/pull/6, @LarsDenBakker)
* Fix comment handling in imports (https://github.com/guybedford/es-module-lexer/issues/8)

0.2.0
* Include CJS build (https://github.com/guybedford/es-module-lexer/pull/1, @LarsDenBakker)
