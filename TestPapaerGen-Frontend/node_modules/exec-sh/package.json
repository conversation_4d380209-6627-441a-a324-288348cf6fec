{"name": "exec-sh", "version": "0.3.6", "description": "Execute shell command forwarding all stdio.", "main": "lib/exec-sh.js", "scripts": {"test": "npm run lint && npm run cover-test", "cover-test": "nyc --reporter=lcov --report-dir=artifacts/coverage mocha", "lint": "standard --verbose **/*.js", "dev": "mocha --reporter spec --watch", "jsdoc": "jsdoc --private --destination artifacts/jsdoc lib/"}, "repository": {"type": "git", "url": "**************:tsertkov/exec-sh.git"}, "keywords": ["exec", "spawn", "terminal", "console", "shell", "command", "child_process"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/tsertkov/exec-sh/issues"}, "devDependencies": {"coveralls": "^3.1.0", "jsdoc": "^3.6.6", "jshint": "^2.12.0", "mocha": "^8.3.2", "nyc": "^15.1.0", "sinon": "^9.2.4", "standard": "^16.0.3"}}