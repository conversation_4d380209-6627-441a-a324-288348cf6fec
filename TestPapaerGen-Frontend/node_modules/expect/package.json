{"name": "expect", "version": "26.6.2", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/expect"}, "license": "MIT", "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@jest/types": "^26.6.2", "ansi-styles": "^4.0.0", "jest-get-type": "^26.3.0", "jest-matcher-utils": "^26.6.2", "jest-message-util": "^26.6.2", "jest-regex-util": "^26.0.0"}, "devDependencies": {"@jest/test-utils": "^26.6.2", "chalk": "^4.0.0", "fast-check": "^2.0.0", "immutable": "^4.0.0-rc.12"}, "engines": {"node": ">= 10.14.2"}, "publishConfig": {"access": "public"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5"}