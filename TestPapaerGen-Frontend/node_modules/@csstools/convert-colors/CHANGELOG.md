# Changes to Convert Colors

### 1.4.0 (January 27, 2018)

- Add LCH conversions
- Allow fallbacks in RGB conversions (for gray conversions)
- Add Lab and LCH tests
- Simplify test array joining
- Rename references from "LAB" to "Lab"

### 1.3.0 (January 25, 2018)

- Export all converter combinations of `rgb`, `hsl`, `hwb`, `lab`, `hsv`,
  `xyz`, and also the `rgb2hue` converter
- Organize library into files

### 1.2.0 (January 24, 2018)

- Export `rgb2lab`, `lab2rgb`, `hsl2lab`, etc. converters

### 1.1.0 (January 22, 2018)

- Export `rgb2hue` converter

### 1.0.0 (January 21, 2018)

- Initial version
