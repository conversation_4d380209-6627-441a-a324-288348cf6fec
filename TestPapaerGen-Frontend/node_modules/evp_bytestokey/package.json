{"name": "evp_bytestokey", "version": "1.0.3", "description": "The insecure key derivation algorithm from OpenSSL", "keywords": ["crypto", "openssl"], "homepage": "https://github.com/crypto-browserify/EVP_BytesToKey", "bugs": {"url": "https://github.com/crypto-browserify/EVP_BytesToKey/issues"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON> <<EMAIL>>"], "files": ["index.js"], "main": "index.js", "repository": {"type": "git", "url": "https://github.com/crypto-browserify/EVP_BytesToKey.git"}, "scripts": {"coverage": "nyc tape test/*.js", "lint": "standard", "test": "npm run lint && npm run unit", "test:prepare": "node-gyp rebuild", "unit": "tape test/*.js"}, "devDependencies": {"bindings": "^1.2.1", "nan": "^2.4.0", "nyc": "^8.1.0", "standard": "^8.0.0", "tape": "^4.6.0"}, "gypfile": false, "dependencies": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}