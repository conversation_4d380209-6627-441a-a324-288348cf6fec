{"version": 3, "sources": ["stackframe.js", "error-stack-parser.js"], "names": ["root", "factory", "define", "amd", "exports", "module", "StackFrame", "this", "_isNumber", "n", "isNaN", "parseFloat", "isFinite", "functionName", "args", "fileName", "lineNumber", "columnNumber", "source", "undefined", "setFunctionName", "set<PERSON>rgs", "setFileName", "setLineNumber", "setColumnNumber", "setSource", "prototype", "getFunctionName", "v", "String", "getArgs", "Object", "toString", "call", "TypeError", "getFileName", "getLineNumber", "Number", "getColumnNumber", "getSource", "join", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_map", "array", "fn", "thisArg", "Array", "map", "output", "length", "i", "_filter", "filter", "push", "_indexOf", "target", "indexOf", "FIREFOX_SAFARI_STACK_REGEXP", "CHROME_IE_STACK_REGEXP", "SAFARI_NATIVE_CODE_REGEXP", "parse", "error", "stacktrace", "parseOpera", "stack", "match", "parseV8OrIE", "parseFFOr<PERSON><PERSON><PERSON>", "Error", "extractLocation", "urlLike", "regExp", "parts", "exec", "replace", "filtered", "split", "line", "tokens", "slice", "locationParts", "pop", "e", "message", "parseOpera9", "parseOpera11", "parseOpera10", "lineRE", "lines", "result", "len", "argsRaw", "functionCall", "shift"], "mappings": "CAAA,SAAAA,EAAAC,GACA,YAIA,mBAAAC,SAAAA,OAAAC,IACAD,OAAA,gBAAAD,GACA,gBAAAG,SACAC,OAAAD,QAAAH,IAEAD,EAAAM,WAAAL,KAEAM,KAAA,WACA,YACA,SAAAC,GAAAC,GACA,OAAAC,MAAAC,WAAAF,KAAAG,SAAAH,GAGA,QAAAH,GAAAO,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GACAC,SAAAN,GACAN,KAAAa,gBAAAP,GAEAM,SAAAL,GACAP,KAAAc,QAAAP,GAEAK,SAAAJ,GACAR,KAAAe,YAAAP,GAEAI,SAAAH,GACAT,KAAAgB,cAAAP,GAEAG,SAAAF,GACAV,KAAAiB,gBAAAP,GAEAE,SAAAD,GACAX,KAAAkB,UAAAP,GAsEA,MAlEAZ,GAAAoB,WACAC,gBAAA,WACA,MAAApB,MAAAM,cAEAO,gBAAA,SAAAQ,GACArB,KAAAM,aAAAgB,OAAAD,IAGAE,QAAA,WACA,MAAAvB,MAAAO,MAEAO,QAAA,SAAAO,GACA,GAAA,mBAAAG,OAAAL,UAAAM,SAAAC,KAAAL,GACA,KAAA,IAAAM,WAAA,wBAEA3B,MAAAO,KAAAc,GAOAO,YAAA,WACA,MAAA5B,MAAAQ,UAEAO,YAAA,SAAAM,GACArB,KAAAQ,SAAAc,OAAAD,IAGAQ,cAAA,WACA,MAAA7B,MAAAS,YAEAO,cAAA,SAAAK,GACA,IAAApB,EAAAoB,GACA,KAAA,IAAAM,WAAA,+BAEA3B,MAAAS,WAAAqB,OAAAT,IAGAU,gBAAA,WACA,MAAA/B,MAAAU,cAEAO,gBAAA,SAAAI,GACA,IAAApB,EAAAoB,GACA,KAAA,IAAAM,WAAA,iCAEA3B,MAAAU,aAAAoB,OAAAT,IAGAW,UAAA,WACA,MAAAhC,MAAAW,QAEAO,UAAA,SAAAG,GACArB,KAAAW,OAAAW,OAAAD,IAGAI,SAAA,WACA,GAAAnB,GAAAN,KAAAoB,mBAAA,cACAb,EAAA,KAAAP,KAAAuB,eAAAU,KAAA,KAAA,IACAzB,EAAAR,KAAA4B,cAAA,IAAA5B,KAAA4B,cAAA,GACAnB,EAAAR,EAAAD,KAAA6B,iBAAA,IAAA7B,KAAA6B,gBAAA,GACAnB,EAAAT,EAAAD,KAAA+B,mBAAA,IAAA/B,KAAA+B,kBAAA,EACA,OAAAzB,GAAAC,EAAAC,EAAAC,EAAAC,IAIAX,ICzGA,SAAAN,EAAAC,GACA,YAIA,mBAAAC,SAAAA,OAAAC,IACAD,OAAA,sBAAA,cAAAD,GACA,gBAAAG,SACAC,OAAAD,QAAAH,EAAAwC,QAAA,eAEAzC,EAAA0C,iBAAAzC,EAAAD,EAAAM,aAEAC,KAAA,SAAAD,GACA,YAMA,SAAAqC,GAAAC,EAAAC,EAAAC,GACA,GAAA,kBAAAC,OAAArB,UAAAsB,IACA,MAAAJ,GAAAI,IAAAH,EAAAC,EAGA,KAAA,GADAG,GAAA,GAAAF,OAAAH,EAAAM,QACAC,EAAA,EAAAA,EAAAP,EAAAM,OAAAC,IACAF,EAAAE,GAAAN,EAAAZ,KAAAa,EAAAF,EAAAO,GAEA,OAAAF,GAIA,QAAAG,GAAAR,EAAAC,EAAAC,GACA,GAAA,kBAAAC,OAAArB,UAAA2B,OACA,MAAAT,GAAAS,OAAAR,EAAAC,EAGA,KAAA,GADAG,MACAE,EAAA,EAAAA,EAAAP,EAAAM,OAAAC,IACAN,EAAAZ,KAAAa,EAAAF,EAAAO,KACAF,EAAAK,KAAAV,EAAAO,GAGA,OAAAF,GAIA,QAAAM,GAAAX,EAAAY,GACA,GAAA,kBAAAT,OAAArB,UAAA+B,QACA,MAAAb,GAAAa,QAAAD,EAEA,KAAA,GAAAL,GAAA,EAAAA,EAAAP,EAAAM,OAAAC,IACA,GAAAP,EAAAO,KAAAK,EACA,MAAAL,EAGA,OAAA,GAvCA,GAAAO,GAAA,gBACAC,EAAA,kCACAC,EAAA,8BAyCA,QAOAC,MAAA,SAAAC,GACA,GAAA,mBAAAA,GAAAC,YAAA,mBAAAD,GAAA,mBACA,MAAAvD,MAAAyD,WAAAF,EACA,IAAAA,EAAAG,OAAAH,EAAAG,MAAAC,MAAAP,GACA,MAAApD,MAAA4D,YAAAL,EACA,IAAAA,EAAAG,MACA,MAAA1D,MAAA6D,gBAAAN,EAEA,MAAA,IAAAO,OAAA,oCAKAC,gBAAA,SAAAC,GAEA,GAAA,KAAAA,EAAAd,QAAA,KACA,OAAAc,EAGA,IAAAC,GAAA,iCACAC,EAAAD,EAAAE,KAAAH,EAAAI,QAAA,UAAA,IACA,QAAAF,EAAA,GAAAA,EAAA,IAAAtD,OAAAsD,EAAA,IAAAtD,SAGAgD,YAAA,SAAAL,GACA,GAAAc,GAAAxB,EAAAU,EAAAG,MAAAY,MAAA,MAAA,SAAAC,GACA,QAAAA,EAAAZ,MAAAP,IACApD,KAEA,OAAAoC,GAAAiC,EAAA,SAAAE,GACAA,EAAArB,QAAA,UAAA,KAEAqB,EAAAA,EAAAH,QAAA,aAAA,QAAAA,QAAA,iCAAA,IAEA,IAAAI,GAAAD,EAAAH,QAAA,OAAA,IAAAA,QAAA,eAAA,KAAAE,MAAA,OAAAG,MAAA,GACAC,EAAA1E,KAAA+D,gBAAAS,EAAAG,OACArE,EAAAkE,EAAAvC,KAAA,MAAArB,OACAJ,EAAAwC,GAAA,OAAA,eAAA0B,EAAA,IAAA,GAAA9D,OAAA8D,EAAA,EAEA,OAAA,IAAA3E,GAAAO,EAAAM,OAAAJ,EAAAkE,EAAA,GAAAA,EAAA,GAAAH,IACAvE,OAGA6D,gBAAA,SAAAN,GACA,GAAAc,GAAAxB,EAAAU,EAAAG,MAAAY,MAAA,MAAA,SAAAC,GACA,OAAAA,EAAAZ,MAAAN,IACArD,KAEA,OAAAoC,GAAAiC,EAAA,SAAAE,GAMA,GAJAA,EAAArB,QAAA,WAAA,KACAqB,EAAAA,EAAAH,QAAA,qDAAA,QAGA,KAAAG,EAAArB,QAAA,MAAA,KAAAqB,EAAArB,QAAA,KAEA,MAAA,IAAAnD,GAAAwE,EAEA,IAAAC,GAAAD,EAAAD,MAAA,KACAI,EAAA1E,KAAA+D,gBAAAS,EAAAG,OACArE,EAAAkE,EAAAvC,KAAA,MAAArB,MACA,OAAA,IAAAb,GAAAO,EACAM,OACA8D,EAAA,GACAA,EAAA,GACAA,EAAA,GACAH,IAEAvE,OAGAyD,WAAA,SAAAmB,GACA,OAAAA,EAAApB,YAAAoB,EAAAC,QAAA3B,QAAA,MAAA,IACA0B,EAAAC,QAAAP,MAAA,MAAA3B,OAAAiC,EAAApB,WAAAc,MAAA,MAAA3B,OACA3C,KAAA8E,YAAAF,GACAA,EAAAlB,MAGA1D,KAAA+E,aAAAH,GAFA5E,KAAAgF,aAAAJ,IAMAE,YAAA,SAAAF,GAKA,IAAA,GAJAK,GAAA,oCACAC,EAAAN,EAAAC,QAAAP,MAAA,MACAa,KAEAvC,EAAA,EAAAwC,EAAAF,EAAAvC,OAAAyC,EAAAxC,EAAAA,GAAA,EAAA,CACA,GAAAe,GAAAsB,EAAAd,KAAAe,EAAAtC,GACAe,IACAwB,EAAApC,KAAA,GAAAhD,GAAAa,OAAAA,OAAA+C,EAAA,GAAAA,EAAA,GAAA/C,OAAAsE,EAAAtC,KAIA,MAAAuC,IAGAH,aAAA,SAAAJ,GAKA,IAAA,GAJAK,GAAA,6DACAC,EAAAN,EAAApB,WAAAc,MAAA,MACAa,KAEAvC,EAAA,EAAAwC,EAAAF,EAAAvC,OAAAyC,EAAAxC,EAAAA,GAAA,EAAA,CACA,GAAAe,GAAAsB,EAAAd,KAAAe,EAAAtC,GACAe,IACAwB,EAAApC,KACA,GAAAhD,GACA4D,EAAA,IAAA/C,OACAA,OACA+C,EAAA,GACAA,EAAA,GACA/C,OACAsE,EAAAtC,KAMA,MAAAuC,IAIAJ,aAAA,SAAAxB,GACA,GAAAc,GAAAxB,EAAAU,EAAAG,MAAAY,MAAA,MAAA,SAAAC,GACA,QAAAA,EAAAZ,MAAAR,KAAAoB,EAAAZ,MAAA,sBACA3D,KAEA,OAAAoC,GAAAiC,EAAA,SAAAE,GACA,GAMAc,GANAb,EAAAD,EAAAD,MAAA,KACAI,EAAA1E,KAAA+D,gBAAAS,EAAAG,OACAW,EAAAd,EAAAe,SAAA,GACAjF,EAAAgF,EACAlB,QAAA,iCAAA,MACAA,QAAA,cAAA,KAAAxD,MAEA0E,GAAA3B,MAAA,kBACA0B,EAAAC,EAAAlB,QAAA,uBAAA,MAEA,IAAA7D,GAAAK,SAAAyE,GAAA,8BAAAA,EACAzE,OAAAyE,EAAAf,MAAA,IACA,OAAA,IAAAvE,GACAO,EACAC,EACAmE,EAAA,GACAA,EAAA,GACAA,EAAA,GACAH,IACAvE", "file": "error-stack-parser.min.js", "sourcesContent": ["(function (root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stackframe', [], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory();\n    } else {\n        root.StackFrame = factory();\n    }\n}(this, function () {\n    'use strict';\n    function _isNumber(n) {\n        return !isNaN(parseFloat(n)) && isFinite(n);\n    }\n\n    function StackFrame(functionName, args, fileName, lineNumber, columnNumber, source) {\n        if (functionName !== undefined) {\n            this.setFunctionName(functionName);\n        }\n        if (args !== undefined) {\n            this.setArgs(args);\n        }\n        if (fileName !== undefined) {\n            this.setFileName(fileName);\n        }\n        if (lineNumber !== undefined) {\n            this.setLineNumber(lineNumber);\n        }\n        if (columnNumber !== undefined) {\n            this.setColumnNumber(columnNumber);\n        }\n        if (source !== undefined) {\n            this.setSource(source);\n        }\n    }\n\n    StackFrame.prototype = {\n        getFunctionName: function () {\n            return this.functionName;\n        },\n        setFunctionName: function (v) {\n            this.functionName = String(v);\n        },\n\n        getArgs: function () {\n            return this.args;\n        },\n        setArgs: function (v) {\n            if (Object.prototype.toString.call(v) !== '[object Array]') {\n                throw new TypeError('Args must be an Array');\n            }\n            this.args = v;\n        },\n\n        // NOTE: Property name may be misleading as it includes the path,\n        // but it somewhat mirrors V8's JavaScriptStackTraceApi\n        // https://code.google.com/p/v8/wiki/JavaScriptStackTraceApi and Gecko's\n        // http://mxr.mozilla.org/mozilla-central/source/xpcom/base/nsIException.idl#14\n        getFileName: function () {\n            return this.fileName;\n        },\n        setFileName: function (v) {\n            this.fileName = String(v);\n        },\n\n        getLineNumber: function () {\n            return this.lineNumber;\n        },\n        setLineNumber: function (v) {\n            if (!_isNumber(v)) {\n                throw new TypeError('Line Number must be a Number');\n            }\n            this.lineNumber = Number(v);\n        },\n\n        getColumnNumber: function () {\n            return this.columnNumber;\n        },\n        setColumnNumber: function (v) {\n            if (!_isNumber(v)) {\n                throw new TypeError('Column Number must be a Number');\n            }\n            this.columnNumber = Number(v);\n        },\n\n        getSource: function () {\n            return this.source;\n        },\n        setSource: function (v) {\n            this.source = String(v);\n        },\n\n        toString: function() {\n            var functionName = this.getFunctionName() || '{anonymous}';\n            var args = '(' + (this.getArgs() || []).join(',') + ')';\n            var fileName = this.getFileName() ? ('@' + this.getFileName()) : '';\n            var lineNumber = _isNumber(this.getLineNumber()) ? (':' + this.getLineNumber()) : '';\n            var columnNumber = _isNumber(this.getColumnNumber()) ? (':' + this.getColumnNumber()) : '';\n            return functionName + args + fileName + lineNumber + columnNumber;\n        }\n    };\n\n    return StackFrame;\n}));\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('error-stack-parser', ['stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('stackframe'));\n    } else {\n        root.ErrorStackParser = factory(root.StackFrame);\n    }\n}(this, function ErrorStackParser(StackFrame) {\n    'use strict';\n\n    var FIREFOX_SAFARI_STACK_REGEXP = /(^|@)\\S+\\:\\d+/;\n    var CHROME_IE_STACK_REGEXP = /^\\s*at .*(\\S+\\:\\d+|\\(native\\))/m;\n    var SAFARI_NATIVE_CODE_REGEXP = /^(eval@)?(\\[native code\\])?$/;\n\n    function _map(array, fn, thisArg) {\n        if (typeof Array.prototype.map === 'function') {\n            return array.map(fn, thisArg);\n        } else {\n            var output = new Array(array.length);\n            for (var i = 0; i < array.length; i++) {\n                output[i] = fn.call(thisArg, array[i]);\n            }\n            return output;\n        }\n    }\n\n    function _filter(array, fn, thisArg) {\n        if (typeof Array.prototype.filter === 'function') {\n            return array.filter(fn, thisArg);\n        } else {\n            var output = [];\n            for (var i = 0; i < array.length; i++) {\n                if (fn.call(thisArg, array[i])) {\n                    output.push(array[i]);\n                }\n            }\n            return output;\n        }\n    }\n\n    function _indexOf(array, target) {\n        if (typeof Array.prototype.indexOf === 'function') {\n            return array.indexOf(target);\n        } else {\n            for (var i = 0; i < array.length; i++) {\n                if (array[i] === target) {\n                    return i;\n                }\n            }\n            return -1;\n        }\n    }\n\n    return {\n        /**\n         * Given an Error object, extract the most information from it.\n         *\n         * @param {Error} error object\n         * @return {Array} of StackFrames\n         */\n        parse: function ErrorStackParser$$parse(error) {\n            if (typeof error.stacktrace !== 'undefined' || typeof error['opera#sourceloc'] !== 'undefined') {\n                return this.parseOpera(error);\n            } else if (error.stack && error.stack.match(CHROME_IE_STACK_REGEXP)) {\n                return this.parseV8OrIE(error);\n            } else if (error.stack) {\n                return this.parseFFOrSafari(error);\n            } else {\n                throw new Error('Cannot parse given Error object');\n            }\n        },\n\n        // Separate line and column numbers from a string of the form: (URI:Line:Column)\n        extractLocation: function ErrorStackParser$$extractLocation(urlLike) {\n            // Fail-fast but return locations like \"(native)\"\n            if (urlLike.indexOf(':') === -1) {\n                return [urlLike];\n            }\n\n            var regExp = /(.+?)(?:\\:(\\d+))?(?:\\:(\\d+))?$/;\n            var parts = regExp.exec(urlLike.replace(/[\\(\\)]/g, ''));\n            return [parts[1], parts[2] || undefined, parts[3] || undefined];\n        },\n\n        parseV8OrIE: function ErrorStackParser$$parseV8OrIE(error) {\n            var filtered = _filter(error.stack.split('\\n'), function(line) {\n                return !!line.match(CHROME_IE_STACK_REGEXP);\n            }, this);\n\n            return _map(filtered, function(line) {\n                if (line.indexOf('(eval ') > -1) {\n                    // Throw away eval information until we implement stacktrace.js/stackframe#8\n                    line = line.replace(/eval code/g, 'eval').replace(/(\\(eval at [^\\()]*)|(\\)\\,.*$)/g, '');\n                }\n                var tokens = line.replace(/^\\s+/, '').replace(/\\(eval code/g, '(').split(/\\s+/).slice(1);\n                var locationParts = this.extractLocation(tokens.pop());\n                var functionName = tokens.join(' ') || undefined;\n                var fileName = _indexOf(['eval', '<anonymous>'], locationParts[0]) > -1 ? undefined : locationParts[0];\n\n                return new StackFrame(functionName, undefined, fileName, locationParts[1], locationParts[2], line);\n            }, this);\n        },\n\n        parseFFOrSafari: function ErrorStackParser$$parseFFOrSafari(error) {\n            var filtered = _filter(error.stack.split('\\n'), function(line) {\n                return !line.match(SAFARI_NATIVE_CODE_REGEXP);\n            }, this);\n\n            return _map(filtered, function(line) {\n                // Throw away eval information until we implement stacktrace.js/stackframe#8\n                if (line.indexOf(' > eval') > -1) {\n                    line = line.replace(/ line (\\d+)(?: > eval line \\d+)* > eval\\:\\d+\\:\\d+/g, ':$1');\n                }\n\n                if (line.indexOf('@') === -1 && line.indexOf(':') === -1) {\n                    // Safari eval frames only have function names and nothing else\n                    return new StackFrame(line);\n                } else {\n                    var tokens = line.split('@');\n                    var locationParts = this.extractLocation(tokens.pop());\n                    var functionName = tokens.join('@') || undefined;\n                    return new StackFrame(functionName,\n                        undefined,\n                        locationParts[0],\n                        locationParts[1],\n                        locationParts[2],\n                        line);\n                }\n            }, this);\n        },\n\n        parseOpera: function ErrorStackParser$$parseOpera(e) {\n            if (!e.stacktrace || (e.message.indexOf('\\n') > -1 &&\n                e.message.split('\\n').length > e.stacktrace.split('\\n').length)) {\n                return this.parseOpera9(e);\n            } else if (!e.stack) {\n                return this.parseOpera10(e);\n            } else {\n                return this.parseOpera11(e);\n            }\n        },\n\n        parseOpera9: function ErrorStackParser$$parseOpera9(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)/i;\n            var lines = e.message.split('\\n');\n            var result = [];\n\n            for (var i = 2, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(new StackFrame(undefined, undefined, match[2], match[1], undefined, lines[i]));\n                }\n            }\n\n            return result;\n        },\n\n        parseOpera10: function ErrorStackParser$$parseOpera10(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)(?:: In function (\\S+))?$/i;\n            var lines = e.stacktrace.split('\\n');\n            var result = [];\n\n            for (var i = 0, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(\n                        new StackFrame(\n                            match[3] || undefined,\n                            undefined,\n                            match[2],\n                            match[1],\n                            undefined,\n                            lines[i]\n                        )\n                    );\n                }\n            }\n\n            return result;\n        },\n\n        // Opera 10.65+ Error.stack very similar to FF/Safari\n        parseOpera11: function ErrorStackParser$$parseOpera11(error) {\n            var filtered = _filter(error.stack.split('\\n'), function(line) {\n                return !!line.match(FIREFOX_SAFARI_STACK_REGEXP) && !line.match(/^Error created at/);\n            }, this);\n\n            return _map(filtered, function(line) {\n                var tokens = line.split('@');\n                var locationParts = this.extractLocation(tokens.pop());\n                var functionCall = (tokens.shift() || '');\n                var functionName = functionCall\n                        .replace(/<anonymous function(: (\\w+))?>/, '$2')\n                        .replace(/\\([^\\)]*\\)/g, '') || undefined;\n                var argsRaw;\n                if (functionCall.match(/\\(([^\\)]*)\\)/)) {\n                    argsRaw = functionCall.replace(/^[^\\(]+\\(([^\\)]*)\\)$/, '$1');\n                }\n                var args = (argsRaw === undefined || argsRaw === '[arguments not available]') ?\n                    undefined : argsRaw.split(',');\n                return new StackFrame(\n                    functionName,\n                    args,\n                    locationParts[0],\n                    locationParts[1],\n                    locationParts[2],\n                    line);\n            }, this);\n        }\n    };\n}));\n\n"], "sourceRoot": "/source/"}