{"name": "error-stack-parser", "description": "Extract meaning from JS Errors", "maintainers": ["<PERSON> <<EMAIL>> (https://www.eriwen.com)", "<PERSON> <vkhomy<PERSON><PERSON>@gmail.com> (https://github.com/victor-ho<PERSON><PERSON><PERSON>)", "<PERSON> (https://github.com/oliversalzburg)"], "version": "1.3.6", "license": "Unlicense", "keywords": ["stacktrace", "error", "stack", "parser"], "homepage": "https://www.stacktracejs.com", "dependencies": {"stackframe": "^0.3.1"}, "repository": {"type": "git", "url": "git://github.com/stacktracejs/error-stack-parser.git"}, "devDependencies": {"colors": "^1.1.2", "del": "^1.2.1", "gulp": "^3.9.0", "gulp-concat": "^2.6.0", "gulp-coveralls": "^0.1.4", "gulp-jshint": "^2.0.0", "gulp-sourcemaps": "^1.6.0", "gulp-uglify": "^1.5.1", "jasmine": "^2.3.2", "jasmine-core": "^2.3.4", "jscs": "^2.9.0", "jsdoc-dash-template": "^1.2.0", "jshint": "^2.8.0", "karma": "~0.13", "karma-chrome-launcher": "^0.1.12", "karma-coverage": "^0.5.3", "karma-firefox-launcher": "^0.1.7", "karma-ie-launcher": "^0.2.0", "karma-jasmine": "^0.3.6", "karma-opera-launcher": "^0.1.0", "karma-phantomjs2-launcher": "^0.3.2", "karma-safari-launcher": "^0.1.1", "karma-sauce-launcher": "^0.3.0", "karma-spec-reporter": "0.0.23", "run-sequence": "^1.1.5"}, "bugs": {"url": "https://github.com/stacktracejs/error-stack-parser/issues"}, "main": "./error-stack-parser.js", "files": ["LICENSE", "README.md", "error-stack-parser.js", "dist/"], "scripts": {"test": "gulp test", "prepublish": "gulp dist"}}