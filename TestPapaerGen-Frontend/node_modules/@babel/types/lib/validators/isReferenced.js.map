{"version": 3, "names": ["isReferenced", "node", "parent", "grandparent", "type", "property", "computed", "object", "init", "body", "key", "superClass", "right", "source", "local", "id"], "sources": ["../../src/validators/isReferenced.ts"], "sourcesContent": ["import type * as t from \"../index.ts\";\n\n/**\n * Check if the input `node` is a reference to a bound variable.\n */\nexport default function isReferenced(\n  node: t.Node,\n  parent: t.Node,\n  grandparent?: t.Node,\n): boolean {\n  switch (parent.type) {\n    // yes: PARENT[NODE]\n    // yes: NODE.child\n    // no: parent.NODE\n    case \"MemberExpression\":\n    case \"OptionalMemberExpression\":\n      if (parent.property === node) {\n        return !!parent.computed;\n      }\n      return parent.object === node;\n\n    case \"JSXMemberExpression\":\n      return parent.object === node;\n    // no: let NODE = init;\n    // yes: let id = NODE;\n    case \"VariableDeclarator\":\n      return parent.init === node;\n\n    // yes: () => NODE\n    // no: (NODE) => {}\n    case \"ArrowFunctionExpression\":\n      return parent.body === node;\n\n    // no: class { #NODE; }\n    // no: class { get #NODE() {} }\n    // no: class { #NODE() {} }\n    // no: class { fn() { return this.#NODE; } }\n    case \"PrivateName\":\n      return false;\n\n    // no: class { NODE() {} }\n    // yes: class { [NODE]() {} }\n    // no: class { foo(NODE) {} }\n    case \"ClassMethod\":\n    case \"ClassPrivateMethod\":\n    case \"ObjectMethod\":\n      if (parent.key === node) {\n        return !!parent.computed;\n      }\n      return false;\n\n    // yes: { [NODE]: \"\" }\n    // no: { NODE: \"\" }\n    // depends: { NODE }\n    // depends: { key: NODE }\n    case \"ObjectProperty\":\n      if (parent.key === node) {\n        return !!parent.computed;\n      }\n      // parent.value === node\n      return !grandparent || grandparent.type !== \"ObjectPattern\";\n    // no: class { NODE = value; }\n    // yes: class { [NODE] = value; }\n    // yes: class { key = NODE; }\n    case \"ClassProperty\":\n    case \"ClassAccessorProperty\":\n      if (parent.key === node) {\n        return !!parent.computed;\n      }\n      return true;\n    case \"ClassPrivateProperty\":\n      return parent.key !== node;\n\n    // no: class NODE {}\n    // yes: class Foo extends NODE {}\n    case \"ClassDeclaration\":\n    case \"ClassExpression\":\n      return parent.superClass === node;\n\n    // yes: left = NODE;\n    // no: NODE = right;\n    case \"AssignmentExpression\":\n      return parent.right === node;\n\n    // no: [NODE = foo] = [];\n    // yes: [foo = NODE] = [];\n    case \"AssignmentPattern\":\n      return parent.right === node;\n\n    // no: NODE: for (;;) {}\n    case \"LabeledStatement\":\n      return false;\n\n    // no: try {} catch (NODE) {}\n    case \"CatchClause\":\n      return false;\n\n    // no: function foo(...NODE) {}\n    case \"RestElement\":\n      return false;\n\n    case \"BreakStatement\":\n    case \"ContinueStatement\":\n      return false;\n\n    // no: function NODE() {}\n    // no: function foo(NODE) {}\n    case \"FunctionDeclaration\":\n    case \"FunctionExpression\":\n      return false;\n\n    // no: export NODE from \"foo\";\n    // no: export * as NODE from \"foo\";\n    case \"ExportNamespaceSpecifier\":\n    case \"ExportDefaultSpecifier\":\n      return false;\n\n    // no: export { foo as NODE };\n    // yes: export { NODE as foo };\n    // no: export { NODE as foo } from \"foo\";\n    case \"ExportSpecifier\":\n      // @ts-expect-error todo(flow->ts): Property 'source' does not exist on type 'AnyTypeAnnotation'.\n      if (grandparent?.source) {\n        return false;\n      }\n      return parent.local === node;\n\n    // no: import NODE from \"foo\";\n    // no: import * as NODE from \"foo\";\n    // no: import { NODE as foo } from \"foo\";\n    // no: import { foo as NODE } from \"foo\";\n    // no: import NODE from \"bar\";\n    case \"ImportDefaultSpecifier\":\n    case \"ImportNamespaceSpecifier\":\n    case \"ImportSpecifier\":\n      return false;\n\n    // no: import \"foo\" assert { NODE: \"json\" }\n    case \"ImportAttribute\":\n      return false;\n\n    // no: <div NODE=\"foo\" />\n    case \"JSXAttribute\":\n      return false;\n\n    // no: [NODE] = [];\n    // no: ({ NODE }) = [];\n    case \"ObjectPattern\":\n    case \"ArrayPattern\":\n      return false;\n\n    // no: new.NODE\n    // no: NODE.target\n    case \"MetaProperty\":\n      return false;\n\n    // yes: type X = { someProperty: NODE }\n    // no: type X = { NODE: OtherType }\n    case \"ObjectTypeProperty\":\n      return parent.key !== node;\n\n    // yes: enum X { Foo = NODE }\n    // no: enum X { NODE }\n    case \"TSEnumMember\":\n      return parent.id !== node;\n\n    // yes: { [NODE]: value }\n    // no: { NODE: value }\n    case \"TSPropertySignature\":\n      if (parent.key === node) {\n        return !!parent.computed;\n      }\n\n      return true;\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;AAKe,SAASA,YAAYA,CAClCC,IAAY,EACZC,MAAc,EACdC,WAAoB,EACX;EACT,QAAQD,MAAM,CAACE,IAAI;IAIjB,KAAK,kBAAkB;IACvB,KAAK,0BAA0B;MAC7B,IAAIF,MAAM,CAACG,QAAQ,KAAKJ,IAAI,EAAE;QAC5B,OAAO,CAAC,CAACC,MAAM,CAACI,QAAQ;MAC1B;MACA,OAAOJ,MAAM,CAACK,MAAM,KAAKN,IAAI;IAE/B,KAAK,qBAAqB;MACxB,OAAOC,MAAM,CAACK,MAAM,KAAKN,IAAI;IAG/B,KAAK,oBAAoB;MACvB,OAAOC,MAAM,CAACM,IAAI,KAAKP,IAAI;IAI7B,KAAK,yBAAyB;MAC5B,OAAOC,MAAM,CAACO,IAAI,KAAKR,IAAI;IAM7B,KAAK,aAAa;MAChB,OAAO,KAAK;IAKd,KAAK,aAAa;IAClB,KAAK,oBAAoB;IACzB,KAAK,cAAc;MACjB,IAAIC,MAAM,CAACQ,GAAG,KAAKT,IAAI,EAAE;QACvB,OAAO,CAAC,CAACC,MAAM,CAACI,QAAQ;MAC1B;MACA,OAAO,KAAK;IAMd,KAAK,gBAAgB;MACnB,IAAIJ,MAAM,CAACQ,GAAG,KAAKT,IAAI,EAAE;QACvB,OAAO,CAAC,CAACC,MAAM,CAACI,QAAQ;MAC1B;MAEA,OAAO,CAACH,WAAW,IAAIA,WAAW,CAACC,IAAI,KAAK,eAAe;IAI7D,KAAK,eAAe;IACpB,KAAK,uBAAuB;MAC1B,IAAIF,MAAM,CAACQ,GAAG,KAAKT,IAAI,EAAE;QACvB,OAAO,CAAC,CAACC,MAAM,CAACI,QAAQ;MAC1B;MACA,OAAO,IAAI;IACb,KAAK,sBAAsB;MACzB,OAAOJ,MAAM,CAACQ,GAAG,KAAKT,IAAI;IAI5B,KAAK,kBAAkB;IACvB,KAAK,iBAAiB;MACpB,OAAOC,MAAM,CAACS,UAAU,KAAKV,IAAI;IAInC,KAAK,sBAAsB;MACzB,OAAOC,MAAM,CAACU,KAAK,KAAKX,IAAI;IAI9B,KAAK,mBAAmB;MACtB,OAAOC,MAAM,CAACU,KAAK,KAAKX,IAAI;IAG9B,KAAK,kBAAkB;MACrB,OAAO,KAAK;IAGd,KAAK,aAAa;MAChB,OAAO,KAAK;IAGd,KAAK,aAAa;MAChB,OAAO,KAAK;IAEd,KAAK,gBAAgB;IACrB,KAAK,mBAAmB;MACtB,OAAO,KAAK;IAId,KAAK,qBAAqB;IAC1B,KAAK,oBAAoB;MACvB,OAAO,KAAK;IAId,KAAK,0BAA0B;IAC/B,KAAK,wBAAwB;MAC3B,OAAO,KAAK;IAKd,KAAK,iBAAiB;MAEpB,IAAIE,WAAW,YAAXA,WAAW,CAAEU,MAAM,EAAE;QACvB,OAAO,KAAK;MACd;MACA,OAAOX,MAAM,CAACY,KAAK,KAAKb,IAAI;IAO9B,KAAK,wBAAwB;IAC7B,KAAK,0BAA0B;IAC/B,KAAK,iBAAiB;MACpB,OAAO,KAAK;IAGd,KAAK,iBAAiB;MACpB,OAAO,KAAK;IAGd,KAAK,cAAc;MACjB,OAAO,KAAK;IAId,KAAK,eAAe;IACpB,KAAK,cAAc;MACjB,OAAO,KAAK;IAId,KAAK,cAAc;MACjB,OAAO,KAAK;IAId,KAAK,oBAAoB;MACvB,OAAOC,MAAM,CAACQ,GAAG,KAAKT,IAAI;IAI5B,KAAK,cAAc;MACjB,OAAOC,MAAM,CAACa,EAAE,KAAKd,IAAI;IAI3B,KAAK,qBAAqB;MACxB,IAAIC,MAAM,CAACQ,GAAG,KAAKT,IAAI,EAAE;QACvB,OAAO,CAAC,CAACC,MAAM,CAACI,QAAQ;MAC1B;MAEA,OAAO,IAAI;EACf;EAEA,OAAO,IAAI;AACb", "ignoreList": []}