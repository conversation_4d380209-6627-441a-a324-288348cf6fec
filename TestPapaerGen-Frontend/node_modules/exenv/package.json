{"name": "exenv", "version": "1.2.2", "description": "React's ExecutionEnvironment module extracted for use in other packages & components", "main": "index.js", "scripts": {"test": "echo \"See React tests\" && exit 0"}, "repository": {"type": "git", "url": "https://github.com/JedWatson/exenv.git"}, "keywords": ["react", "browser", "server", "environment", "env", "execution", "executionenvironment"], "author": "<PERSON>", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/JedWatson/exenv/issues"}, "homepage": "https://github.com/JedWatson/exenv"}