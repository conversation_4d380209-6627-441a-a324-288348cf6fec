/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @providesModule fetch
 */

'use strict';

// This hopefully supports the React Native case, which is already bringing along
// its own fetch polyfill. That should exist on `global`. If that doesn't exist
// then we'll try to polyfill, which might not work correctly in all environments.

if (global.fetch) {
  module.exports = global.fetch.bind(global);
} else {
  module.exports = require('isomorphic-fetch');
}