"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.renderLayoutSettingItem = exports.default = void 0;

require("antd/lib/switch/style");

var _switch = _interopRequireDefault(require("antd/lib/switch"));

require("antd/lib/select/style");

var _select = _interopRequireDefault(require("antd/lib/select"));

require("antd/lib/list/style");

var _list = _interopRequireDefault(require("antd/lib/list"));

require("antd/lib/tooltip/style");

var _tooltip = _interopRequireDefault(require("antd/lib/tooltip"));

var _jsxRuntime = require("react/jsx-runtime");

var _react = _interopRequireDefault(require("react"));

var _defaultSettings = _interopRequireDefault(require("../../defaultSettings"));

var _index = require("./index");

var renderLayoutSettingItem = function renderLayoutSettingItem(item) {
  var action = /*#__PURE__*/_react.default.cloneElement(item.action, {
    disabled: item.disabled
  });

  return (0, _jsxRuntime.jsx)(_tooltip.default, {
    title: item.disabled ? item.disabledReason : '',
    placement: "left",
    children: (0, _jsxRuntime.jsx)(_list.default.Item, {
      actions: [action],
      children: (0, _jsxRuntime.jsx)("span", {
        style: {
          opacity: item.disabled ? 0.5 : 1
        },
        children: item.title
      })
    })
  });
};

exports.renderLayoutSettingItem = renderLayoutSettingItem;

var LayoutSetting = function LayoutSetting(_ref) {
  var _ref$settings = _ref.settings,
      settings = _ref$settings === void 0 ? {} : _ref$settings,
      changeSetting = _ref.changeSetting;
  var formatMessage = (0, _index.getFormatMessage)();

  var _ref2 = settings || _defaultSettings.default,
      contentWidth = _ref2.contentWidth,
      splitMenus = _ref2.splitMenus,
      fixedHeader = _ref2.fixedHeader,
      layout = _ref2.layout,
      fixSiderbar = _ref2.fixSiderbar;

  return (0, _jsxRuntime.jsx)(_list.default, {
    split: false,
    dataSource: [{
      title: formatMessage({
        id: 'app.setting.content-width',
        defaultMessage: 'Content Width'
      }),
      action: (0, _jsxRuntime.jsxs)(_select.default, {
        value: contentWidth || 'Fixed',
        size: "small",
        className: "content-width",
        onSelect: function onSelect(value) {
          changeSetting('contentWidth', value);
        },
        style: {
          width: 80
        },
        children: [layout === 'side' ? null : (0, _jsxRuntime.jsx)(_select.default.Option, {
          value: "Fixed",
          children: formatMessage({
            id: 'app.setting.content-width.fixed',
            defaultMessage: 'Fixed'
          })
        }), (0, _jsxRuntime.jsx)(_select.default.Option, {
          value: "Fluid",
          children: formatMessage({
            id: 'app.setting.content-width.fluid',
            defaultMessage: 'Fluid'
          })
        })]
      })
    }, {
      title: formatMessage({
        id: 'app.setting.fixedheader',
        defaultMessage: 'Fixed Header'
      }),
      action: (0, _jsxRuntime.jsx)(_switch.default, {
        size: "small",
        className: "fixed-header",
        checked: !!fixedHeader,
        onChange: function onChange(checked) {
          changeSetting('fixedHeader', checked);
        }
      })
    }, {
      title: formatMessage({
        id: 'app.setting.fixedsidebar',
        defaultMessage: 'Fixed Sidebar'
      }),
      disabled: layout === 'top',
      disabledReason: formatMessage({
        id: 'app.setting.fixedsidebar.hint',
        defaultMessage: 'Works on Side Menu Layout'
      }),
      action: (0, _jsxRuntime.jsx)(_switch.default, {
        size: "small",
        className: "fix-siderbar",
        checked: !!fixSiderbar,
        onChange: function onChange(checked) {
          return changeSetting('fixSiderbar', checked);
        }
      })
    }, {
      title: formatMessage({
        id: 'app.setting.splitMenus'
      }),
      disabled: layout !== 'mix',
      action: (0, _jsxRuntime.jsx)(_switch.default, {
        size: "small",
        checked: !!splitMenus,
        className: "split-menus",
        onChange: function onChange(checked) {
          changeSetting('splitMenus', checked);
        }
      })
    }],
    renderItem: renderLayoutSettingItem
  });
};

var _default = LayoutSetting;
exports.default = _default;