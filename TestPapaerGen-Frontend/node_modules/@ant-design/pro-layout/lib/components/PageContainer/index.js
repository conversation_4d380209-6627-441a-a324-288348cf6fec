"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.ProPageHeader = exports.ProBreadcrumb = void 0;

require("antd/lib/affix/style");

var _affix = _interopRequireDefault(require("antd/lib/affix"));

var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));

require("antd/lib/config-provider/style");

var _configProvider = _interopRequireDefault(require("antd/lib/config-provider"));

require("antd/lib/page-header/style");

var _pageHeader = _interopRequireDefault(require("antd/lib/page-header"));

var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));

require("antd/lib/breadcrumb/style");

var _breadcrumb = _interopRequireDefault(require("antd/lib/breadcrumb"));

var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));

require("antd/lib/tabs/style");

var _tabs = _interopRequireDefault(require("antd/lib/tabs"));

var _typeof2 = _interopRequireDefault(require("@babel/runtime/helpers/typeof"));

var _jsxRuntime = require("react/jsx-runtime");

var _react = _interopRequireWildcard(require("react"));

var _classnames = _interopRequireDefault(require("classnames"));

var _RouteContext = _interopRequireDefault(require("../../RouteContext"));

var _FooterToolbar = _interopRequireDefault(require("../FooterToolbar"));

var _GridContent = _interopRequireDefault(require("../GridContent"));

var _PageLoading = _interopRequireDefault(require("../PageLoading"));

var _WaterMark = _interopRequireDefault(require("../WaterMark"));

require("./index.less");

var _excluded = ["title", "content", "pageHeaderRender", "header", "prefixedClassName", "extraContent", "style", "prefixCls", "breadcrumbRender"],
    _excluded2 = ["children", "loading", "className", "style", "footer", "affixProps", "ghost", "fixedHeader", "breadcrumbRender"];

function genLoading(spinProps) {
  if ((0, _typeof2.default)(spinProps) === 'object') {
    return spinProps;
  }

  return {
    spinning: spinProps
  };
}
/**
 * Render Footer tabList In order to be compatible with the old version of the PageHeader basically
 * all the functions are implemented.
 */


var renderFooter = function renderFooter(_ref) {
  var tabList = _ref.tabList,
      tabActiveKey = _ref.tabActiveKey,
      onTabChange = _ref.onTabChange,
      tabBarExtraContent = _ref.tabBarExtraContent,
      tabProps = _ref.tabProps,
      prefixedClassName = _ref.prefixedClassName;

  if (Array.isArray(tabList) || tabBarExtraContent) {
    return (0, _jsxRuntime.jsx)(_tabs.default, (0, _objectSpread2.default)((0, _objectSpread2.default)({
      className: "".concat(prefixedClassName, "-tabs"),
      activeKey: tabActiveKey,
      onChange: function onChange(key) {
        if (onTabChange) {
          onTabChange(key);
        }
      },
      tabBarExtraContent: tabBarExtraContent
    }, tabProps), {}, {
      children: tabList === null || tabList === void 0 ? void 0 : tabList.map(function (item, index) {
        return (
          /*#__PURE__*/
          // eslint-disable-next-line react/no-array-index-key
          (0, _react.createElement)(_tabs.default.TabPane, (0, _objectSpread2.default)((0, _objectSpread2.default)({}, item), {}, {
            tab: item.tab,
            key: item.key || index
          }))
        );
      })
    }));
  }

  return null;
};

var renderPageHeader = function renderPageHeader(content, extraContent, prefixedClassName) {
  if (!content && !extraContent) {
    return null;
  }

  return (0, _jsxRuntime.jsx)("div", {
    className: "".concat(prefixedClassName, "-detail"),
    children: (0, _jsxRuntime.jsx)("div", {
      className: "".concat(prefixedClassName, "-main"),
      children: (0, _jsxRuntime.jsxs)("div", {
        className: "".concat(prefixedClassName, "-row"),
        children: [content && (0, _jsxRuntime.jsx)("div", {
          className: "".concat(prefixedClassName, "-content"),
          children: content
        }), extraContent && (0, _jsxRuntime.jsx)("div", {
          className: "".concat(prefixedClassName, "-extraContent"),
          children: extraContent
        })]
      })
    })
  });
};
/**
 * 配置与面包屑相同，只是增加了自动根据路由计算面包屑的功能。此功能必须要在 ProLayout 中使用。
 *
 * @param props
 * @returns
 */


var ProBreadcrumb = function ProBreadcrumb(props) {
  var value = (0, _react.useContext)(_RouteContext.default);
  return (0, _jsxRuntime.jsx)("div", {
    style: {
      height: '100%',
      display: 'flex',
      alignItems: 'center'
    },
    children: (0, _jsxRuntime.jsx)(_breadcrumb.default, (0, _objectSpread2.default)((0, _objectSpread2.default)((0, _objectSpread2.default)({}, value === null || value === void 0 ? void 0 : value.breadcrumb), value === null || value === void 0 ? void 0 : value.breadcrumbProps), props))
  });
};

exports.ProBreadcrumb = ProBreadcrumb;

var ProPageHeader = function ProPageHeader(props) {
  var _breadcrumb$routes;

  var value = (0, _react.useContext)(_RouteContext.default);
  var title = props.title,
      content = props.content,
      pageHeaderRender = props.pageHeaderRender,
      header = props.header,
      prefixedClassName = props.prefixedClassName,
      extraContent = props.extraContent,
      style = props.style,
      prefixCls = props.prefixCls,
      breadcrumbRender = props.breadcrumbRender,
      restProps = (0, _objectWithoutProperties2.default)(props, _excluded);
  var getBreadcrumbRender = (0, _react.useMemo)(function () {
    if (!breadcrumbRender) {
      return undefined;
    }

    return breadcrumbRender;
  }, [breadcrumbRender]);

  if (pageHeaderRender === false) {
    return null;
  }

  if (pageHeaderRender) {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [" ", pageHeaderRender((0, _objectSpread2.default)((0, _objectSpread2.default)({}, props), value))]
    });
  }

  var pageHeaderTitle = title;

  if (!title && title !== false) {
    pageHeaderTitle = value.title;
  }

  var pageHeaderProps = (0, _objectSpread2.default)((0, _objectSpread2.default)((0, _objectSpread2.default)({}, value), {}, {
    title: pageHeaderTitle
  }, restProps), {}, {
    footer: renderFooter((0, _objectSpread2.default)((0, _objectSpread2.default)({}, restProps), {}, {
      breadcrumbRender: breadcrumbRender,
      prefixedClassName: prefixedClassName
    }))
  }, header);
  var breadcrumb = pageHeaderProps.breadcrumb;
  var noHasBreadCrumb = (!breadcrumb || !(breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.itemRender) && !(breadcrumb === null || breadcrumb === void 0 ? void 0 : (_breadcrumb$routes = breadcrumb.routes) === null || _breadcrumb$routes === void 0 ? void 0 : _breadcrumb$routes.length)) && !breadcrumbRender;

  if (['title', 'subTitle', 'extra', 'tags', 'footer', 'avatar', 'backIcon'].every(function (item) {
    return !pageHeaderProps[item];
  }) && noHasBreadCrumb && !content && !extraContent) {
    return null;
  }

  return (0, _jsxRuntime.jsx)("div", {
    className: "".concat(prefixedClassName, "-warp"),
    children: (0, _jsxRuntime.jsx)(_pageHeader.default, (0, _objectSpread2.default)((0, _objectSpread2.default)({}, pageHeaderProps), {}, {
      breadcrumb: breadcrumbRender === false ? undefined : (0, _objectSpread2.default)((0, _objectSpread2.default)({}, pageHeaderProps.breadcrumb), value.breadcrumbProps),
      breadcrumbRender: getBreadcrumbRender,
      prefixCls: prefixCls,
      children: (header === null || header === void 0 ? void 0 : header.children) || renderPageHeader(content, extraContent, prefixedClassName)
    }))
  });
};

exports.ProPageHeader = ProPageHeader;

var PageContainer = function PageContainer(props) {
  var _classNames, _restProps$header2;

  var children = props.children,
      _props$loading = props.loading,
      loading = _props$loading === void 0 ? false : _props$loading,
      className = props.className,
      style = props.style,
      footer = props.footer,
      affixProps = props.affixProps,
      ghost = props.ghost,
      fixedHeader = props.fixedHeader,
      breadcrumbRender = props.breadcrumbRender,
      restProps = (0, _objectWithoutProperties2.default)(props, _excluded2);
  var value = (0, _react.useContext)(_RouteContext.default);

  var _useContext = (0, _react.useContext)(_configProvider.default.ConfigContext),
      getPrefixCls = _useContext.getPrefixCls;

  var prefixCls = props.prefixCls || getPrefixCls('pro');
  var prefixedClassName = "".concat(prefixCls, "-page-container");
  var containerClassName = (0, _classnames.default)(prefixedClassName, className, (_classNames = {}, (0, _defineProperty2.default)(_classNames, "".concat(prefixCls, "-page-container-ghost"), ghost), (0, _defineProperty2.default)(_classNames, "".concat(prefixCls, "-page-container-with-footer"), footer), _classNames));
  var content = (0, _react.useMemo)(function () {
    return children ? (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)("div", {
        className: "".concat(prefixedClassName, "-children-content"),
        children: children
      }), value.hasFooterToolbar && (0, _jsxRuntime.jsx)("div", {
        style: {
          height: 48,
          marginTop: 24
        }
      })]
    }) : null;
  }, [children, prefixedClassName, value.hasFooterToolbar]);
  var memoBreadcrumbRender = (0, _react.useMemo)(function () {
    var _restProps$header;

    if (breadcrumbRender == false) return false;
    return breadcrumbRender || (restProps === null || restProps === void 0 ? void 0 : (_restProps$header = restProps.header) === null || _restProps$header === void 0 ? void 0 : _restProps$header.breadcrumbRender);
  }, [breadcrumbRender, restProps === null || restProps === void 0 ? void 0 : (_restProps$header2 = restProps.header) === null || _restProps$header2 === void 0 ? void 0 : _restProps$header2.breadcrumbRender]);
  var pageHeaderDom = (0, _jsxRuntime.jsx)(ProPageHeader, (0, _objectSpread2.default)((0, _objectSpread2.default)({}, restProps), {}, {
    breadcrumbRender: memoBreadcrumbRender,
    ghost: ghost,
    prefixCls: undefined,
    prefixedClassName: prefixedClassName
  }));
  var loadingDom = (0, _react.useMemo)(function () {
    // 当loading时一个合法的ReactNode时，说明用户使用了自定义loading,直接返回改自定义loading
    if ( /*#__PURE__*/_react.default.isValidElement(loading)) {
      return loading;
    } // 当传递过来的是布尔值，并且为false时，说明不需要显示loading,返回null


    if (typeof loading === 'boolean' && !loading) {
      return null;
    } // 如非上述两种情况，那么要么用户传了一个true,要么用户传了loading配置，使用genLoading生成loading配置后返回PageLoading


    var spinProps = genLoading(loading); // 如果传的是loading配置，但spinning传的是false，也不需要显示loading

    return spinProps.spinning ? (0, _jsxRuntime.jsx)(_PageLoading.default, (0, _objectSpread2.default)({}, spinProps)) : null;
  }, [loading]);
  var renderContentDom = (0, _react.useMemo)(function () {
    // 只要loadingDom非空我们就渲染loadingDom,否则渲染内容
    var dom = loadingDom || content;

    if (props.waterMarkProps || value.waterMarkProps) {
      var waterMarkProps = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, value.waterMarkProps), props.waterMarkProps);
      return (0, _jsxRuntime.jsx)(_WaterMark.default, (0, _objectSpread2.default)((0, _objectSpread2.default)({}, waterMarkProps), {}, {
        children: dom
      }));
    }

    return dom;
  }, [props.waterMarkProps, value.waterMarkProps, loadingDom, content]);
  return (0, _jsxRuntime.jsxs)("div", {
    style: style,
    className: containerClassName,
    children: [fixedHeader && pageHeaderDom ? // 在 hasHeader 且 fixedHeader 的情况下，才需要设置高度
    (0, _jsxRuntime.jsx)(_affix.default, (0, _objectSpread2.default)((0, _objectSpread2.default)({
      offsetTop: value.hasHeader && value.fixedHeader ? value.headerHeight : 0
    }, affixProps), {}, {
      children: pageHeaderDom
    })) : pageHeaderDom, renderContentDom && (0, _jsxRuntime.jsx)(_GridContent.default, {
      children: renderContentDom
    }), footer && (0, _jsxRuntime.jsx)(_FooterToolbar.default, {
      prefixCls: prefixCls,
      children: footer
    })]
  });
};

var _default = PageContainer;
exports.default = _default;