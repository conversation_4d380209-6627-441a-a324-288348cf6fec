"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.isPlainObj = isPlainObj;

var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));

var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));

var _typeof2 = _interopRequireDefault(require("@babel/runtime/helpers/typeof"));

var _get = _interopRequireDefault(require("rc-util/lib/utils/get"));

var _set = _interopRequireDefault(require("rc-util/lib/utils/set"));

var _react = _interopRequireDefault(require("react"));

var _isNil = _interopRequireDefault(require("../isNil"));

var _merge = require("../merge");

/**
 * 暂时还不支持 Set和 Map 结构 判断是不是一个能遍历的对象
 *
 * @param itemValue
 * @returns Boolean
 */
function isPlainObj(itemValue) {
  if ((0, _typeof2.default)(itemValue) !== 'object') return false;
  /** Null 也要处理，不然omit空会失效 */

  if (itemValue === null) return true;
  if ( /*#__PURE__*/_react.default.isValidElement(itemValue)) return false;
  if (itemValue.constructor === RegExp) return false;
  if (itemValue instanceof Map) return false;
  if (itemValue instanceof Set) return false;
  if (itemValue instanceof HTMLElement) return false;
  if (itemValue instanceof Blob) return false;
  if (itemValue instanceof File) return false;
  if (Array.isArray(itemValue)) return false;
  return true;
}

var transformKeySubmitValue = function transformKeySubmitValue(values, dataFormatMapRaw) {
  var omit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
  // ignore nil transform
  var dataFormatMap = Object.keys(dataFormatMapRaw).reduce(function (ret, key) {
    var value = dataFormatMapRaw[key];

    if (!(0, _isNil.default)(value)) {
      // eslint-disable-next-line no-param-reassign
      ret[key] = value; // can't be undefined
    }

    return ret;
  }, {});

  if (Object.keys(dataFormatMap).length < 1) {
    return values;
  }

  if (typeof window === 'undefined') return values; // 如果 value 是 string | null | Array | Blob类型 其中之一，直接返回
  // 形如 {key: [File, File]} 的表单字段当进行第二次递归时会导致其直接越过 typeof value !== 'object' 这一判断 https://github.com/ant-design/pro-components/issues/2071

  if ((0, _typeof2.default)(values) !== 'object' || (0, _isNil.default)(values) || values instanceof Blob) {
    return values;
  }

  var finalValues = Array.isArray(values) ? [] : {};

  var gen = function gen(tempValues, parentsKey) {
    var isArrayValues = Array.isArray(tempValues);
    var result = isArrayValues ? [] : {};

    if (tempValues == null || tempValues === undefined) {
      return result;
    }

    Object.keys(tempValues).forEach(function (entityKey) {
      var key = parentsKey ? [parentsKey, entityKey].flat(1) : [entityKey].flat(1);
      var itemValue = tempValues[entityKey];
      var transformFunction = (0, _get.default)(dataFormatMap, key);

      var _transformArray = function _transformArray(transformFn) {
        if (!Array.isArray(transformFn)) return entityKey;
        transformFn.forEach(function (fn, idx) {
          if (!fn) return;

          if (typeof fn === 'function') {
            itemValue[idx] = fn(itemValue, entityKey, tempValues);
          }

          if ((0, _typeof2.default)(fn) === 'object' && !Array.isArray(fn)) {
            Object.keys(fn).forEach(function (curK) {
              if (typeof fn[curK] === 'function') {
                var res = fn[curK](tempValues[entityKey][idx][curK], entityKey, tempValues);
                itemValue[idx][curK] = (0, _typeof2.default)(res) === 'object' ? res[curK] : res;
              }
            });
          }

          if ((0, _typeof2.default)(fn) === 'object' && Array.isArray(fn)) {
            _transformArray(fn);
          }
        });
        return entityKey;
      };

      var transform = function transform() {
        var tempKey = typeof transformFunction === 'function' ? transformFunction === null || transformFunction === void 0 ? void 0 : transformFunction(itemValue, entityKey, tempValues) : _transformArray(transformFunction); // { [key:string]:any } 数组也能通过编译

        if (Array.isArray(tempKey)) {
          result = (0, _set.default)(result, tempKey, itemValue);
          return;
        }

        if ((0, _typeof2.default)(tempKey) === 'object' && !Array.isArray(finalValues)) {
          finalValues = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, finalValues), tempKey);
        } else if ((0, _typeof2.default)(tempKey) === 'object' && Array.isArray(finalValues)) {
          result = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, result), tempKey);
        } else if (tempKey) {
          result = (0, _set.default)(result, [tempKey], itemValue);
        }
      };
      /** 如果存在转化器提前渲染一下 */


      if (transformFunction && typeof transformFunction === 'function') {
        transform();
      }

      if (typeof window === 'undefined') return;

      if (isPlainObj(itemValue)) {
        var genValues = gen(itemValue, key);

        if (Object.keys(genValues).length < 1) {
          return;
        }

        result = (0, _set.default)(result, [entityKey], genValues);
        return;
      }

      transform();
    }); // namePath、transform在omit为false时需正常返回 https://github.com/ant-design/pro-components/issues/2901#issue-908097115

    return omit ? result : tempValues;
  };

  finalValues = Array.isArray(values) && Array.isArray(finalValues) ? (0, _toConsumableArray2.default)(gen(values)) : (0, _merge.merge)({}, gen(values), finalValues);
  return finalValues;
};

var _default = transformKeySubmitValue;
exports.default = _default;