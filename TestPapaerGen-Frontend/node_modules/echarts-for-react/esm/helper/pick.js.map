{"version": 3, "file": "pick.js", "sourceRoot": "", "sources": ["../../src/helper/pick.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,MAAM,UAAU,IAAI,CAAC,GAA4B,EAAE,IAAc;IAC/D,IAAM,CAAC,GAAG,EAAE,CAAC;IACb,IAAI,CAAC,OAAO,CAAC,UAAC,GAAG;QACf,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,CAAC;AACX,CAAC", "sourcesContent": ["/**\n * 保留 object 中的部分内容\n * @param obj\n * @param keys\n */\nexport function pick(obj: Record<string, unknown>, keys: string[]): Record<string, unknown> {\n  const r = {};\n  keys.forEach((key) => {\n    r[key] = obj[key];\n  });\n  return r;\n}\n"]}