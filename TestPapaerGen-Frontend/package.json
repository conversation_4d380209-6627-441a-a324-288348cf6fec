{"private": true, "scripts": {"start": "umi dev", "dev": "umi dev", "dev:windows": "set NODE_OPTIONS=--max-old-space-size=4096 && umi dev", "build": "umi build", "build:windows": "set NODE_OPTIONS=--max-old-space-size=4096 && umi build", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "clean": "rimraf node_modules .umi dist", "reinstall": "npm run clean && npm install", "windows:diagnosis": "node windows-diagnosis.js", "windows:check-paths": "node check-paths.js", "windows:fix": "windows-fix-all.bat", "windows:start": "start-windows.bat", "china:fix": "fix-china-network.bat", "china:mirror": "npm config set registry https://registry.npmmirror.com/", "dev:china": "npm run china:mirror && npm run dev:windows", "debug:login": "node debug-login.js", "check:backend": "check-backend.bat", "fix:network": "node fix-network-issues.js", "dev:offline": "set DISABLE_EXTERNAL_REQUESTS=true && npm run dev:windows"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@umijs/preset-react": "1.x", "@umijs/test": "^3.0.14", "echarts": "^5.3.2", "echarts-for-react": "^3.0.2", "lint-staged": "^10.0.7", "moment": "^2.29.3", "prettier": "^1.19.1", "rc-queue-anim": "^1.8.4", "rc-texty": "^0.2.0", "rc-tween-one": "latest", "react": "^16.12.0", "react-dom": "^16.12.0", "react-highlight-words": "^0.16.0", "umi": "^3.5.23", "yorkie": "^2.0.0", "antd": "^4.20.2"}, "devDependencies": {"enquire-js": "^0.2.1", "rc-scroll-anim": "^2.7.4"}}