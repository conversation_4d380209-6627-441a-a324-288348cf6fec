@echo off
echo ========================================
echo   Windows 一键修复工具
echo   Test Paper Generation System
echo ========================================
echo.

echo [诊断] 运行系统诊断...
node windows-diagnosis.js
echo.

echo [步骤1] 停止所有相关进程...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im npm.exe >nul 2>&1
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq umi*" >nul 2>&1
echo ✅ 进程已停止

echo [步骤2] 清理所有缓存和临时文件...
if exist ".umi" (
    echo 删除 .umi 目录...
    rmdir /s /q ".umi"
)
if exist "dist" (
    echo 删除 dist 目录...
    rmdir /s /q "dist"
)
if exist "node_modules\.cache" (
    echo 删除 node_modules 缓存...
    rmdir /s /q "node_modules\.cache"
)
if exist ".umi-production" (
    echo 删除 .umi-production 目录...
    rmdir /s /q ".umi-production"
)
if exist "package-lock.json" (
    echo 删除 package-lock.json...
    del "package-lock.json"
)
echo ✅ 缓存已清理

echo [步骤3] 清理npm缓存...
npm cache clean --force
echo ✅ npm缓存已清理

echo [步骤4] 检查并修复依赖...
if not exist "node_modules" (
    echo 安装依赖...
    npm install
) else (
    echo 验证依赖完整性...
    npm ls --depth=0 >nul 2>&1
    if %errorlevel% neq 0 (
        echo 重新安装依赖...
        rmdir /s /q "node_modules"
        npm install
    ) else (
        echo ✅ 依赖完整
    )
)

echo [步骤5] 检查路径兼容性...
node check-paths.js

echo [步骤6] 验证配置文件...
if exist ".umirc.ts" (
    echo ✅ UMI配置文件存在
) else (
    echo ❌ UMI配置文件缺失
    goto :error
)

echo [步骤7] 检查端口占用...
netstat -ano | findstr :8000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  端口8000被占用，尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000') do (
        taskkill /f /pid %%a >nul 2>&1
    )
    echo ✅ 端口已释放
) else (
    echo ✅ 端口8000可用
)

echo [步骤8] 设置环境变量...
set NODE_OPTIONS=--max-old-space-size=4096
set FORCE_COLOR=1
set UMI_ENV=development
echo ✅ 环境变量已设置

echo.
echo ========================================
echo   修复完成，准备启动服务器
echo ========================================
echo.

echo 🚀 启动开发服务器...
echo 📝 如果启动失败，请检查以下内容：
echo    1. Node.js版本是否为16.x或18.x
echo    2. 是否以管理员身份运行
echo    3. 防火墙是否阻止了Node.js
echo.

npm run dev:windows

if %errorlevel% neq 0 (
    echo.
    echo ❌ 启动失败，请查看错误信息
    echo 💡 建议：
    echo    1. 检查Node.js版本: node --version
    echo    2. 以管理员身份重新运行此脚本
    echo    3. 检查防火墙设置
    echo    4. 联系技术支持
    goto :error
)

echo.
echo ✅ 服务器启动成功！
echo 🌐 访问地址: http://localhost:8000
goto :end

:error
echo.
echo ❌ 修复过程中出现错误
echo 📞 请联系技术支持并提供以上错误信息
pause
exit /b 1

:end
echo.
echo 服务器已停止
pause
