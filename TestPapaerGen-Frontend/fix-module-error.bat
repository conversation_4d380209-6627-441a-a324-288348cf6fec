@echo off
echo ========================================
echo   Module Error Fix Script
echo   For Windows Deployment Issues
echo ========================================
echo.

echo [Step 1] Stopping any running processes...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im npm.exe >nul 2>&1
echo ✅ Processes stopped

echo [Step 2] Cleaning all cache and build files...
if exist ".umi" (
    echo Removing .umi directory...
    rmdir /s /q ".umi"
)
if exist "dist" (
    echo Removing dist directory...
    rmdir /s /q "dist"
)
if exist "node_modules\.cache" (
    echo Removing node_modules cache...
    rmdir /s /q "node_modules\.cache"
)
if exist "package-lock.json" (
    echo Removing package-lock.json...
    del "package-lock.json"
)
echo ✅ Cache cleaned

echo [Step 3] Cleaning npm cache...
npm cache clean --force
echo ✅ npm cache cleaned

echo [Step 4] Reinstalling dependencies...
if exist "node_modules" (
    echo Removing existing node_modules...
    rmdir /s /q "node_modules"
)
echo Installing fresh dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Error: Failed to install dependencies
    echo Try running this script as Administrator
    pause
    exit /b 1
)
echo ✅ Dependencies installed

echo [Step 5] Setting up environment...
set NODE_OPTIONS=--max-old-space-size=4096
set FORCE_COLOR=1
set UMI_ENV=development

echo [Step 6] Starting with clean environment...
echo.
echo 🚀 Attempting to start server...
echo 📝 If you still see module errors, press Ctrl+C and contact support
echo.

npm run dev:windows

echo.
echo Script completed.
pause
