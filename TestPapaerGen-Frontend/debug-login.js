const http = require('http');
const https = require('https');

console.log('🔍 登录问题调试工具\n');

// 测试后端连接
const testBackendConnection = () => {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 8081,
      path: '/getLoginStatus',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      console.log(`✅ 后端响应状态: ${res.statusCode}`);
      resolve({ success: true, status: res.statusCode });
    });

    req.on('error', (err) => {
      console.log(`❌ 后端连接失败: ${err.message}`);
      resolve({ success: false, error: err.message });
    });

    req.on('timeout', () => {
      console.log('❌ 后端连接超时');
      req.destroy();
      resolve({ success: false, error: 'timeout' });
    });

    req.end();
  });
};

// 测试登录API
const testLoginAPI = () => {
  return new Promise((resolve) => {
    const postData = JSON.stringify({
      username: 'test',
      password: 'test'
    });

    const options = {
      hostname: 'localhost',
      port: 8081,
      path: '/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        console.log(`✅ 登录API响应状态: ${res.statusCode}`);
        console.log(`📄 响应内容: ${data.substring(0, 200)}...`);
        resolve({ success: true, status: res.statusCode, data });
      });
    });

    req.on('error', (err) => {
      console.log(`❌ 登录API连接失败: ${err.message}`);
      resolve({ success: false, error: err.message });
    });

    req.on('timeout', () => {
      console.log('❌ 登录API连接超时');
      req.destroy();
      resolve({ success: false, error: 'timeout' });
    });

    req.write(postData);
    req.end();
  });
};

// 检查前端配置
const checkFrontendConfig = () => {
  console.log('📋 检查前端配置...');
  
  try {
    const fs = require('fs');
    const configPath = 'src/config/requestConfig.js';
    
    if (fs.existsSync(configPath)) {
      const config = fs.readFileSync(configPath, 'utf8');
      console.log('✅ 前端配置文件存在');
      console.log('📄 配置内容:');
      console.log(config);
      
      // 检查API地址配置
      if (config.includes(':8081')) {
        console.log('✅ 后端端口配置正确 (8081)');
      } else {
        console.log('⚠️ 后端端口配置可能有问题');
      }
    } else {
      console.log('❌ 前端配置文件不存在');
    }
  } catch (error) {
    console.log(`❌ 读取配置文件失败: ${error.message}`);
  }
};

// 生成解决方案
const generateSolutions = (backendResult, loginResult) => {
  console.log('\n🔧 问题诊断和解决方案:\n');
  
  if (!backendResult.success) {
    console.log('🚨 主要问题: 后端服务未启动或无法访问');
    console.log('\n📋 解决步骤:');
    console.log('1. 启动后端服务:');
    console.log('   cd TestPapaerGen-Backend');
    console.log('   mvn spring-boot:run');
    console.log('   或在IDE中运行 TestPaperGenerationApplication.java');
    console.log('');
    console.log('2. 检查数据库服务:');
    console.log('   - 确保MySQL/PostgreSQL服务正在运行');
    console.log('   - 检查数据库连接配置');
    console.log('');
    console.log('3. 检查端口占用:');
    console.log('   netstat -ano | findstr :8081');
    console.log('');
    return;
  }
  
  if (backendResult.success && backendResult.status === 403) {
    console.log('✅ 后端服务正常运行');
    console.log('✅ 登录状态检查API正常 (返回403表示未登录，这是正常的)');
  }
  
  if (!loginResult.success) {
    console.log('⚠️ 登录API测试失败');
    console.log('💡 可能原因:');
    console.log('   - 数据库连接问题');
    console.log('   - 用户表不存在或为空');
    console.log('   - 后端业务逻辑错误');
  } else {
    console.log('✅ 登录API可以访问');
    if (loginResult.status === 403) {
      console.log('✅ 登录验证正常工作 (测试用户不存在，返回403)');
    }
  }
  
  console.log('\n🎯 前端登录问题可能原因:');
  console.log('1. 浏览器控制台有JavaScript错误');
  console.log('2. 网络请求被CORS策略阻止');
  console.log('3. 前端路由配置问题');
  console.log('4. 登录成功但跳转逻辑有问题');
  
  console.log('\n🔍 调试建议:');
  console.log('1. 打开浏览器开发者工具 (F12)');
  console.log('2. 查看Console标签页的错误信息');
  console.log('3. 查看Network标签页的网络请求');
  console.log('4. 尝试手动输入正确的用户名密码');
  console.log('5. 检查登录成功后的跳转逻辑');
};

// 主函数
const main = async () => {
  console.log('🔍 开始诊断登录问题...\n');
  
  // 检查前端配置
  checkFrontendConfig();
  
  console.log('\n🌐 测试后端连接...');
  const backendResult = await testBackendConnection();
  
  console.log('\n🔐 测试登录API...');
  const loginResult = await testLoginAPI();
  
  // 生成解决方案
  generateSolutions(backendResult, loginResult);
  
  console.log('\n✅ 诊断完成');
  console.log('\n💡 提示: 如果问题仍然存在，请:');
  console.log('1. 运行 check-backend.bat 检查后端状态');
  console.log('2. 查看浏览器开发者工具的错误信息');
  console.log('3. 检查后端日志文件');
};

// 运行诊断
main().catch(console.error);
