const fs = require('fs');
const path = require('path');

console.log('🌐 网络问题修复工具\n');

// 1. 检查ECharts相关文件
console.log('📊 检查ECharts配置...');
const echartsFiles = [
  'src/pages/questionGenerator/index.js',
  'src/pages/questionBank/overViewModal.js', 
  'src/pages/questionGenHistory/reportDrawer.js'
];

let hasNetworkIssues = false;

echartsFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    
    // 检查是否使用了旧的导入方式
    const hasOldImport = content.includes("import 'echarts/lib/");
    const hasFullImport = content.includes("import * as echarts from 'echarts';");
    const hasMapComponent = content.includes('MapChart') || content.includes('GeoComponent');
    
    console.log(`   ${file}:`);
    console.log(`     旧版导入: ${hasOldImport ? '❌' : '✅'}`);
    console.log(`     完整导入: ${hasFullImport ? '❌' : '✅'}`);
    console.log(`     地图组件: ${hasMapComponent ? '⚠️' : '✅'}`);
    
    if (hasOldImport || hasFullImport || hasMapComponent) {
      hasNetworkIssues = true;
    }
  } else {
    console.log(`   ❌ 文件不存在: ${file}`);
  }
});

// 2. 检查package.json中的ECharts版本
console.log('\n📦 检查ECharts版本...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const echartsVersion = packageJson.dependencies?.echarts;
  const echartsForReactVersion = packageJson.dependencies?.['echarts-for-react'];
  
  console.log(`   echarts版本: ${echartsVersion || '未安装'}`);
  console.log(`   echarts-for-react版本: ${echartsForReactVersion || '未安装'}`);
  
  if (!echartsVersion || !echartsForReactVersion) {
    console.log('   ⚠️ ECharts依赖缺失');
    hasNetworkIssues = true;
  }
} catch (e) {
  console.log('   ❌ 无法读取package.json');
}

// 3. 检查网络配置
console.log('\n🔧 检查UMI配置...');
try {
  const umiConfig = fs.readFileSync('.umirc.ts', 'utf8');
  const hasNetworkConfig = umiConfig.includes('headers') && umiConfig.includes('Access-Control-Allow-Origin');
  const hasExternals = umiConfig.includes('externals');
  
  console.log(`   CORS配置: ${hasNetworkConfig ? '✅' : '❌'}`);
  console.log(`   外部依赖配置: ${hasExternals ? '✅' : '❌'}`);
  
  if (!hasNetworkConfig || !hasExternals) {
    hasNetworkIssues = true;
  }
} catch (e) {
  console.log('   ❌ 无法读取.umirc.ts');
}

// 4. 检查是否存在ECharts配置工具
console.log('\n🛠️ 检查ECharts工具...');
const echartsConfigExists = fs.existsSync('src/utils/echartsConfig.js');
console.log(`   ECharts配置工具: ${echartsConfigExists ? '✅' : '❌'}`);

if (!echartsConfigExists) {
  hasNetworkIssues = true;
}

// 5. 生成修复建议
console.log('\n📋 诊断结果:');
if (hasNetworkIssues) {
  console.log('   ⚠️ 发现网络相关问题');
  console.log('\n🔧 修复建议:');
  console.log('   1. 使用按需导入ECharts组件');
  console.log('   2. 避免使用地图相关功能');
  console.log('   3. 配置离线模式');
  console.log('   4. 设置CORS头部');
  console.log('   5. 使用本地资源');
} else {
  console.log('   ✅ 未发现明显的网络问题');
}

// 6. 自动修复功能
console.log('\n🚀 自动修复选项:');
console.log('   运行以下命令进行修复:');
console.log('   - npm run windows:fix (完整修复)');
console.log('   - 手动更新ECharts导入方式');
console.log('   - 使用离线模式启动');

// 7. 创建离线启动脚本
console.log('\n📝 创建离线模式配置...');
const offlineConfig = `
// 离线模式配置
window.ECHARTS_OFFLINE_MODE = true;
window.DISABLE_EXTERNAL_REQUESTS = true;

// 禁用外部网络请求
if (typeof fetch !== 'undefined') {
  const originalFetch = fetch;
  window.fetch = function(url, options) {
    // 检查是否为外部URL
    if (typeof url === 'string' && (
      url.includes('api.icity.org') ||
      url.includes('cdn.') ||
      url.includes('unpkg.com') ||
      url.includes('jsdelivr.net')
    )) {
      console.warn('🚫 已阻止外部网络请求:', url);
      return Promise.reject(new Error('外部网络请求已被禁用'));
    }
    return originalFetch.call(this, url, options);
  };
}

console.log('✅ 离线模式已启用');
`;

try {
  fs.writeFileSync('public/offline-config.js', offlineConfig);
  console.log('   ✅ 离线配置文件已创建: public/offline-config.js');
} catch (e) {
  console.log('   ❌ 无法创建离线配置文件');
}

// 8. 检查常见的网络错误
console.log('\n🔍 常见网络错误检查:');
const commonErrors = [
  'ERR_CONNECTION_REFUSED',
  'TypeError: Failed to fetch',
  'api.icity.org',
  'CORS error',
  'Network request failed'
];

console.log('   如果遇到以下错误，请使用此工具修复:');
commonErrors.forEach(error => {
  console.log(`   - ${error}`);
});

console.log('\n✅ 网络问题诊断完成');
console.log('💡 提示: 如果问题仍然存在，请检查防火墙和代理设置');
