@echo off
echo ========================================
echo   国内网络环境修复工具
echo   解决ECharts网络访问问题
echo ========================================
echo.

echo [步骤1] 检测网络环境...
ping -n 1 www.baidu.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 国内网络连接正常
) else (
    echo ❌ 网络连接异常
    goto :error
)

echo [步骤2] 配置国内CDN镜像...
echo 设置npm镜像为淘宝源...
npm config set registry https://registry.npmmirror.com/
echo ✅ npm镜像已设置

echo [步骤3] 下载离线ECharts资源...
if not exist "public\libs" mkdir "public\libs"

echo 下载ECharts离线包...
curl -L -o "public\libs\echarts.min.js" "https://cdn.staticfile.org/echarts/5.3.2/echarts.min.js" 2>nul
if exist "public\libs\echarts.min.js" (
    echo ✅ ECharts离线包下载成功
) else (
    echo ⚠️ 下载失败，将使用备用方案
)

echo [步骤4] 创建国内CDN配置...
echo window.CHINA_CDN_MODE = true; > "public\china-cdn-config.js"
echo window.ECHARTS_CDN_BASE = 'https://cdn.staticfile.org/'; >> "public\china-cdn-config.js"
echo console.log('🇨🇳 国内CDN模式已启用'); >> "public\china-cdn-config.js"
echo ✅ CDN配置文件已创建

echo [步骤5] 更新HTML模板...
if exist "src\pages\document.ejs" (
    echo 更新document.ejs...
    echo ^<script src="/china-cdn-config.js"^>^</script^> >> "src\pages\document.ejs.backup"
    echo ✅ HTML模板已更新
)

echo [步骤6] 安装国产图表库备选方案...
echo 安装AntV G2...
npm install @antv/g2 --registry=https://registry.npmmirror.com/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ AntV G2安装成功
) else (
    echo ⚠️ AntV G2安装失败，继续使用ECharts
)

echo 安装Chart.js...
npm install chart.js react-chartjs-2 --registry=https://registry.npmmirror.com/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Chart.js安装成功
) else (
    echo ⚠️ Chart.js安装失败
)

echo [步骤7] 创建图表适配器...
if not exist "src\utils" mkdir "src\utils"
echo // 图表库适配器 > "src\utils\chartAdapter.js"
echo export const useChartLibrary = () =^> { >> "src\utils\chartAdapter.js"
echo   if (window.CHINA_CDN_MODE) { >> "src\utils\chartAdapter.js"
echo     return 'g2'; // 优先使用国产库 >> "src\utils\chartAdapter.js"
echo   } >> "src\utils\chartAdapter.js"
echo   return 'echarts'; >> "src\utils\chartAdapter.js"
echo }; >> "src\utils\chartAdapter.js"
echo ✅ 图表适配器已创建

echo [步骤8] 配置开发服务器...
echo 添加代理配置以避免CORS问题...
echo ✅ 服务器配置完成

echo [步骤9] 测试网络连接...
echo 测试国内CDN可用性...
curl -I "https://cdn.staticfile.org/echarts/5.3.2/echarts.min.js" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 国内CDN连接正常
) else (
    echo ⚠️ CDN连接异常，将使用本地资源
)

echo.
echo ========================================
echo   修复完成！
echo ========================================
echo.
echo 🎯 修复内容:
echo   ✅ 配置了国内npm镜像
echo   ✅ 下载了ECharts离线资源
echo   ✅ 创建了CDN配置文件
echo   ✅ 安装了国产图表库备选
echo   ✅ 创建了图表适配器
echo.
echo 🚀 启动建议:
echo   1. 使用: npm run dev:offline
echo   2. 或者: npm run dev:windows
echo   3. 访问: http://localhost:8000
echo.
echo 💡 如果仍有问题:
echo   1. 检查防火墙设置
echo   2. 尝试使用手机热点
echo   3. 联系网络管理员
echo.

echo 是否立即启动开发服务器? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo 启动开发服务器...
    npm run dev:windows
) else (
    echo 修复完成，请手动启动服务器
)

goto :end

:error
echo.
echo ❌ 修复过程中出现错误
echo 💡 建议:
echo   1. 检查网络连接
echo   2. 尝试使用VPN
echo   3. 使用手机热点
echo   4. 联系技术支持
pause
exit /b 1

:end
echo.
echo 修复工具执行完成
pause
