const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🔍 Windows部署问题诊断工具\n');

// 1. 系统环境检查
console.log('📋 系统环境检查:');
console.log(`   操作系统: ${os.type()} ${os.release()}`);
console.log(`   Node.js版本: ${process.version}`);
console.log(`   平台: ${process.platform}`);
console.log(`   架构: ${process.arch}`);

// 2. 检查关键文件和目录
console.log('\n📁 关键文件检查:');
const criticalPaths = [
  'package.json',
  '.umirc.ts', 
  'src/layouts/index.js',
  'src/pages/login/index.js',
  'src/pages/home/<USER>',
  'src/pages/admin/index.js',
  'src/pages/questionBank/index.js',
  'src/pages/questionEdit/index.js',
  'src/pages/questionGenerator/index.js',
  'src/pages/questionGenHistory/index.js'
];

let missingFiles = [];
criticalPaths.forEach(filePath => {
  const exists = fs.existsSync(path.join(__dirname, filePath));
  console.log(`   ${exists ? '✅' : '❌'} ${filePath}`);
  if (!exists) missingFiles.push(filePath);
});

// 3. 检查依赖完整性
console.log('\n📦 依赖检查:');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredDeps = ['umi', 'react', 'react-dom', 'antd', '@umijs/preset-react'];

requiredDeps.forEach(dep => {
  const hasInDeps = packageJson.dependencies && packageJson.dependencies[dep];
  const hasInDevDeps = packageJson.devDependencies && packageJson.devDependencies[dep];
  const exists = hasInDeps || hasInDevDeps;
  console.log(`   ${exists ? '✅' : '❌'} ${dep}`);
});

// 4. 检查UMI配置
console.log('\n⚙️  UMI配置检查:');
try {
  const umiConfig = fs.readFileSync('.umirc.ts', 'utf8');
  const hasWindowsDetection = umiConfig.includes('process.platform');
  const hasMfsuDisabled = umiConfig.includes('mfsu: false') || umiConfig.includes('mfsu: isWindows ? false');
  const hasDevServer = umiConfig.includes('devServer');
  
  console.log(`   ${hasWindowsDetection ? '✅' : '❌'} Windows平台检测`);
  console.log(`   ${hasMfsuDisabled ? '✅' : '❌'} MFSU Windows兼容配置`);
  console.log(`   ${hasDevServer ? '✅' : '❌'} 开发服务器配置`);
} catch (e) {
  console.log('   ❌ 无法读取.umirc.ts配置文件');
}

// 5. 检查Windows特定脚本
console.log('\n🪟 Windows工具检查:');
const windowsTools = [
  'start-windows.bat',
  'fix-module-error.bat', 
  'check-server.bat',
  'windows-diagnosis.js'
];

windowsTools.forEach(tool => {
  const exists = fs.existsSync(tool);
  console.log(`   ${exists ? '✅' : '❌'} ${tool}`);
});

// 6. 生成诊断报告
console.log('\n📊 诊断结果:');
if (missingFiles.length > 0) {
  console.log(`   ⚠️  发现 ${missingFiles.length} 个缺失文件`);
  console.log('   建议: 检查项目完整性');
}

if (process.platform === 'win32') {
  console.log('   ✅ 当前运行在Windows平台');
  console.log('\n🎯 Windows部署建议:');
  console.log('   1. 运行: fix-module-error.bat (清理缓存)');
  console.log('   2. 运行: start-windows.bat (启动服务器)');
  console.log('   3. 如遇问题: check-server.bat (检查状态)');
} else {
  console.log('   ℹ️  当前不在Windows平台运行');
}

console.log('\n🔧 常见问题快速修复:');
console.log('   模块错误: fix-module-error.bat');
console.log('   端口占用: taskkill /f /im node.exe');
console.log('   权限问题: 以管理员身份运行命令提示符');
console.log('   内存不足: 已在脚本中设置NODE_OPTIONS');

console.log('\n✅ 诊断完成');
