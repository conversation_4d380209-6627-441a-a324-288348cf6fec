@echo off
echo ========================================
echo   后端服务检查工具
echo   检查登录问题
echo ========================================
echo.

echo [1] 检查后端端口8081...
netstat -ano | findstr :8081
if %errorlevel% equ 0 (
    echo ✅ 后端端口8081正在使用
) else (
    echo ❌ 后端端口8081未被占用 - 后端服务可能未启动
    echo.
    echo 💡 解决方案:
    echo    1. 启动后端服务 (Spring Boot应用)
    echo    2. 检查后端项目是否正确配置
    echo    3. 确认数据库连接正常
    goto :backend_not_running
)

echo.
echo [2] 测试后端API连接...
curl -s http://localhost:8081/getLoginStatus >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 后端API响应正常
) else (
    echo ❌ 后端API无响应
    echo.
    echo 💡 可能原因:
    echo    1. 后端服务启动失败
    echo    2. 端口配置错误
    echo    3. 防火墙阻止连接
)

echo.
echo [3] 检查前端配置...
if exist "src\config\requestConfig.js" (
    echo ✅ 前端配置文件存在
    type "src\config\requestConfig.js"
    echo.
) else (
    echo ❌ 前端配置文件缺失
)

echo.
echo [4] 测试完整登录流程...
echo 测试登录API...
curl -X POST -H "Content-Type: application/json" -d "{\"username\":\"test\",\"password\":\"test\"}" http://localhost:8081/login >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 登录API可访问
) else (
    echo ❌ 登录API不可访问
)

echo.
echo [5] 检查Java进程...
tasklist | findstr java.exe
if %errorlevel% equ 0 (
    echo ✅ 发现Java进程
) else (
    echo ❌ 未发现Java进程 - 后端服务未运行
)

echo.
echo ========================================
echo   诊断完成
echo ========================================
goto :end

:backend_not_running
echo.
echo 🚨 后端服务未启动！
echo.
echo 📋 启动后端服务的步骤:
echo    1. 打开后端项目目录: TestPapaerGen-Backend
echo    2. 确保已安装Java 8或更高版本
echo    3. 确保数据库服务正在运行
echo    4. 运行以下命令之一:
echo.
echo       方法1 - 使用Maven:
echo       mvn spring-boot:run
echo.
echo       方法2 - 使用IDE:
echo       在IDE中运行主类 TestPaperGenerationApplication
echo.
echo       方法3 - 使用JAR包:
echo       java -jar target/test-paper-generation-*.jar
echo.
echo 🔧 常见问题解决:
echo    - 端口被占用: 修改application.properties中的server.port
echo    - 数据库连接失败: 检查数据库配置和服务状态
echo    - 依赖问题: 运行 mvn clean install
echo.

:end
echo.
echo 💡 下一步建议:
echo    1. 如果后端未启动，请先启动后端服务
echo    2. 如果后端已启动但无响应，检查日志文件
echo    3. 确认数据库服务正常运行
echo    4. 检查防火墙和网络设置
echo.
pause
